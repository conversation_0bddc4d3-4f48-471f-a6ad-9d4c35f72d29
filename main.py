#!/usr/bin/env python
"""
记忆向量存储服务入口点
"""
import sys
import os
import logging
import uvicorn
from core.app import create_app, create_health_app
from config.settings import service_config

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def main():
    """主函数，启动服务"""
    # 输出环境信息
    env = os.environ.get("ENV", "dev")
    logging.info(f"当前环境: {env}")

    # 检查命令行参数
    if len(sys.argv) > 1 and sys.argv[1] == "health":
        # 启动健康检查服务
        logging.info("启动健康检查服务")
        app = create_health_app()
        uvicorn.run(app, host=service_config["host"], port=service_config["health_port"])
    else:
        # 启动主服务
        logging.info("启动主服务")
        app = create_app()
        uvicorn.run(app, host=service_config["host"], port=service_config["port"])

if __name__ == '__main__':
    main()

