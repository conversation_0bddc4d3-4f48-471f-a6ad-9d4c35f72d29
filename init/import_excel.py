"""
从Excel文件导入数据到OceanBase的agent_memory表
"""
import os
import pandas as pd
import logging
from langchain_openai import OpenAIEmbeddings
from typing import List, Dict, Any
import json

# 导入自定义模块
from utils.snowflake import generate_id
from db.memory_store import AgentMemoryStore
from config.settings import connection_args, openai_config

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,  # 修改为 DEBUG 级别以显示详细信息
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),  # 输出到控制台
    ]
)
logger = logging.getLogger("ImportExcel")

def read_excel(file_path: str) -> pd.DataFrame:
    """
    读取Excel文件

    Args:
        file_path: Excel文件路径

    Returns:
        DataFrame: 包含Excel数据的DataFrame
    """
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"文件不存在: {file_path}")

    logger.info(f"读取Excel文件: {file_path}")
    df = pd.read_excel(file_path)
    logger.info(f"成功读取{len(df)}行数据")

    return df

def prepare_data(df: pd.DataFrame) -> List[Dict[str, Any]]:
    """
    准备要插入的数据

    Args:
        df: 包含Excel数据的DataFrame

    Returns:
        List[Dict[str, Any]]: 准备好的数据列表
    """
    data_list = []

    # 处理每一行数据
    for _, row in df.iterrows():
        # 生成雪花ID
        memory_id = generate_id()

        # 准备数据字典
        data = {
            "id": memory_id,
            "agent_id": "lele",
            "domain": str(row.get("领域", "")).strip(),
            "memory_type": str(row.get("类型", "")).strip(),
            "content_type": str(row.get("contentType", "")).strip(),
            "topic": str(row.get("记忆话题", "")).strip() if pd.notna(row.get("记忆话题", None)) else None,
            "content": row.get("记忆内容", ""),
            "importance": float(row.get("重要度", 0.5)),
            "confidence": float(row.get("可信度", 0.9)),
            "updated_at": row.get("更新日期"),
            "ttl": row.get("时效性") if pd.notna(row.get("时效性", None)) else None,
            "related_user_id": str(row.get("relatedUserId", "")).strip() if pd.notna(row.get("relatedUserId", None)) else None,
        }

        # 处理metadata字段
        metadata = row.get("metadata")
        if pd.notna(metadata):
            # 如果metadata是字符串，尝试解析为JSON
            if isinstance(metadata, str):
                try:
                    metadata = json.loads(metadata)
                except json.JSONDecodeError:
                    # 如果解析失败，将其作为普通字符串处理
                    metadata = {"text": metadata}
            data["metadata"] = json.dumps(metadata, ensure_ascii=False)
        else:
            data["metadata"] = None

        data_list.append(data)

    logger.info(f"准备了{len(data_list)}条数据")
    return data_list

def generate_embeddings_batch(data_batch: List[Dict[str, Any]], embeddings_model) -> List[Dict[str, Any]]:
    """
    为一批数据生成向量嵌入

    Args:
        data_batch: 准备好的数据批次
        embeddings_model: 嵌入模型

    Returns:
        List[Dict[str, Any]]: 包含向量嵌入的数据列表
    """
    logger.info(f"开始为{len(data_batch)}条数据生成向量嵌入")

    for i, data in enumerate(data_batch):
        try:
            content = data["content"]
            # 生成内容的向量嵌入
            logger.debug(f"正在为第{i+1}条数据生成向量嵌入，内容长度: {len(content)}")
            logger.debug(f"内容类型: {type(content)}, 内容预览: {repr(content[:100])}")

            embedding = embeddings_model.embed_query(content)

            # 检查嵌入结果
            if not isinstance(embedding, list) or len(embedding) == 0:
                logger.error(f"第{i+1}条数据的向量嵌入生成失败，结果类型: {type(embedding)}")
                continue

            data["embedding"] = embedding

            # 每处理5条数据或最后一条数据打印一次进度
            if (i + 1) % 5 == 0 or (i + 1) == len(data_batch):
                logger.info(f"已处理 {i + 1}/{len(data_batch)} 条数据")

        except Exception as e:
            logger.error(f"第{i+1}条数据生成向量嵌入失败: {str(e)}")
            logger.error(f"失败的数据内容: {data.get('content', 'N/A')[:100]}...")
            continue

    # 过滤掉没有嵌入的数据
    valid_data = [data for data in data_batch if "embedding" in data]
    logger.info(f"向量嵌入生成完成，成功: {len(valid_data)}/{len(data_batch)} 条")

    return valid_data

def import_data(file_path: str):
    """
    从Excel文件导入数据到OceanBase

    Args:
        file_path: Excel文件路径
    """
    try:
        # 读取Excel文件
        df = read_excel(file_path)

        # 准备数据
        data_list = prepare_data(df)

        # 初始化embedding模型
        logger.info("初始化OpenAI Embeddings模型")
        embeddings = OpenAIEmbeddings(
            model=openai_config["model"],
            openai_api_key=openai_config["openai_api_key"],
            openai_api_base=openai_config["openai_api_base"],
            # 跳过上下文长度检查，直接发送文本
            check_embedding_ctx_length=False
        )
        logger.info("OpenAI Embeddings模型初始化成功")

        # 生成向量嵌入（分批处理）
        logger.info(f"开始为{len(data_list)}条数据生成向量嵌入")

        # 分批处理向量嵌入，避免一次处理太多数据导致超时
        embedding_batch_size = 20  # 每批处理20条数据生成嵌入
        processed_data = []

        for i in range(0, len(data_list), embedding_batch_size):
            batch = data_list[i:i + embedding_batch_size]
            logger.info(f"正在处理第{i//embedding_batch_size + 1}批向量嵌入，数量: {len(batch)}")

            try:
                batch_with_embeddings = generate_embeddings_batch(batch, embeddings)
                processed_data.extend(batch_with_embeddings)
                logger.info(f"第{i//embedding_batch_size + 1}批向量嵌入完成，成功: {len(batch_with_embeddings)}/{len(batch)}条")

                # 每批之间稍作停顿，避免请求频率过高
                import time
                time.sleep(0.5)

            except Exception as e:
                logger.error(f"第{i//embedding_batch_size + 1}批向量嵌入失败: {str(e)}")
                # 继续处理下一批，不中断整个过程
                continue

        data_list = processed_data
        logger.info(f"向量嵌入生成完成，最终数据量: {len(data_list)}条")

        # 初始化记忆存储
        logger.info(f"连接到数据库: {connection_args['host']}:{connection_args['port']}")
        memory_store = AgentMemoryStore(connection_args, embeddings)

        # 批量插入数据
        logger.info(f"开始批量插入{len(data_list)}条数据")

        # 使用AgentMemoryStore的批量插入方法，它有重连机制
        batch_size = 50  # 每批处理50条数据
        total_inserted = 0

        for i in range(0, len(data_list), batch_size):
            batch = data_list[i:i + batch_size]
            try:
                logger.info(f"正在插入第{i//batch_size + 1}批数据，数量: {len(batch)}")

                # 使用upsert方法插入数据
                memory_store.client.upsert(
                    table_name=memory_store.table_name,
                    data=batch
                )

                total_inserted += len(batch)
                logger.info(f"第{i//batch_size + 1}批数据插入成功，已插入: {total_inserted}/{len(data_list)}")

                # 每批之间稍作停顿，避免连接压力过大
                import time
                time.sleep(0.1)

            except Exception as e:
                logger.error(f"第{i//batch_size + 1}批数据插入失败: {str(e)}")
                # 尝试重新连接
                try:
                    memory_store._reconnect()
                    logger.info("数据库重连成功，继续插入")

                    # 重试当前批次
                    memory_store.client.upsert(
                        table_name=memory_store.table_name,
                        data=batch
                    )
                    total_inserted += len(batch)
                    logger.info(f"第{i//batch_size + 1}批数据重试插入成功")

                except Exception as retry_e:
                    logger.error(f"第{i//batch_size + 1}批数据重试插入仍然失败: {str(retry_e)}")
                    raise

        logger.info(f"成功导入{total_inserted}条数据到OceanBase")

    except Exception as e:
        logger.error(f"导入数据失败: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        raise

if __name__ == "__main__":
    # 获取当前脚本所在目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    # 构建Excel文件路径
    excel_file = "/Users/<USER>/PycharmProjects/memory-server-new/init/Agent-new.xlsx"

    # 导入数据
    import_data(excel_file)
