#!/usr/bin/env python3
"""
健壮的Excel数据导入脚本
包含完整的错误处理、重试机制和批量处理
"""
import os
import sys
import pandas as pd
import logging
import time
from typing import List, Dict, Any
from langchain_openai import OpenAIEmbeddings

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.settings import connection_args, openai_config
from db.memory_store import AgentMemoryStore
from utils.snowflake import generate_id
from utils.feishu_alarm import send_error_alarm, send_info_alarm

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),  # 输出到控制台
        logging.FileHandler('import_excel.log', encoding='utf-8')  # 输出到文件
    ]
)
logger = logging.getLogger("ImportExcelRobust")

class ExcelImporter:
    """Excel数据导入器"""
    
    def __init__(self):
        """初始化导入器"""
        self.embeddings = None
        self.memory_store = None
        self.total_processed = 0
        self.total_success = 0
        self.total_failed = 0
    
    def initialize_models(self):
        """初始化模型和存储"""
        try:
            # 初始化embedding模型
            logger.info("初始化OpenAI Embeddings模型")
            logger.info(f"模型配置: {openai_config['model']}, API Base: {openai_config['openai_api_base']}")
            
            self.embeddings = OpenAIEmbeddings(
                model=openai_config["model"],
                openai_api_key=openai_config["openai_api_key"],
                openai_api_base=openai_config["openai_api_base"],
                # 跳过上下文长度检查，直接发送文本
                check_embedding_ctx_length=False
            )
            logger.info("OpenAI Embeddings模型初始化成功")
            
            # 初始化记忆存储
            logger.info(f"连接到数据库: {connection_args['host']}:{connection_args['port']}")
            self.memory_store = AgentMemoryStore(connection_args, self.embeddings)
            logger.info("数据库连接成功")
            
        except Exception as e:
            error_msg = f"模型初始化失败: {str(e)}"
            logger.error(error_msg)
            send_error_alarm(error_msg, "模型初始化失败", str(e))
            raise
    
    def read_excel(self, file_path: str) -> pd.DataFrame:
        """读取Excel文件"""
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        logger.info(f"读取Excel文件: {file_path}")
        df = pd.read_excel(file_path)
        logger.info(f"成功读取{len(df)}行数据")
        
        return df
    
    def prepare_data(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """准备要插入的数据"""
        data_list = []
        
        for index, row in df.iterrows():
            try:
                # 安全获取内容字段
                content = row.get("记忆内容", "")
                if pd.isna(content) or content is None:
                    logger.warning(f"第{index+1}行的记忆内容为空，跳过")
                    continue
                
                # 确保content是字符串类型
                content = str(content).strip()
                if not content:
                    logger.warning(f"第{index+1}行的记忆内容为空字符串，跳过")
                    continue
                
                # 生成雪花ID
                memory_id = generate_id()
                
                # 准备数据字典
                data = {
                    "id": memory_id,
                    "agent_id": "lele",
                    "domain": str(row.get("领域", "")).strip(),
                    "memory_type": str(row.get("类型", "")).strip(),
                    "content_type": str(row.get("contentType", "")).strip(),
                    "topic": str(row.get("记忆话题", "")).strip() if pd.notna(row.get("记忆话题", None)) else None,
                    "content": content,
                    "importance": float(row.get("重要度", 0.5)),
                    "confidence": float(row.get("可信度", 0.9)),
                    "updated_at": row.get("更新日期"),
                    "ttl": row.get("ttl") if pd.notna(row.get("ttl", None)) else None,
                    "related_user_id": str(row.get("relatedUserId", "")).strip() if pd.notna(row.get("relatedUserId", None)) else None,
                }
                
                data_list.append(data)
                
            except Exception as e:
                logger.error(f"准备第{index+1}行数据失败: {str(e)}")
                continue
        
        logger.info(f"数据准备完成，有效数据: {len(data_list)}条")
        return data_list
    
    def generate_embedding_with_retry(self, content: str, max_retries: int = 3) -> List[float]:
        """生成向量嵌入，带重试机制"""
        for attempt in range(max_retries):
            try:
                # 确保内容是字符串类型且不为空
                if not isinstance(content, str):
                    content = str(content)
                
                content = content.strip()
                if not content:
                    raise ValueError("内容为空")
                
                # 生成向量嵌入
                embedding = self.embeddings.embed_query(content)
                
                # 检查嵌入结果
                if not isinstance(embedding, list) or len(embedding) == 0:
                    raise ValueError(f"嵌入结果异常，类型: {type(embedding)}")
                
                return embedding
                
            except Exception as e:
                logger.warning(f"第{attempt + 1}次尝试生成向量嵌入失败: {str(e)}")
                if attempt < max_retries - 1:
                    time.sleep(1)  # 等待1秒后重试
                else:
                    raise
    
    def process_embeddings_batch(self, data_batch: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """批量处理向量嵌入"""
        logger.info(f"开始为{len(data_batch)}条数据生成向量嵌入")
        valid_data = []
        
        for i, data in enumerate(data_batch):
            try:
                content = data["content"]
                logger.debug(f"正在处理第{i+1}条数据，内容长度: {len(content)}")
                
                # 生成向量嵌入
                embedding = self.generate_embedding_with_retry(content)
                data["embedding"] = embedding
                valid_data.append(data)
                
                self.total_success += 1
                
                # 每处理5条数据或最后一条数据打印一次进度
                if (i + 1) % 5 == 0 or (i + 1) == len(data_batch):
                    logger.info(f"已处理 {i + 1}/{len(data_batch)} 条数据，成功: {len(valid_data)}")
                
            except Exception as e:
                logger.error(f"第{i+1}条数据生成向量嵌入失败: {str(e)}")
                logger.error(f"失败的数据内容: {data.get('content', 'N/A')[:100]}...")
                self.total_failed += 1
                continue
        
        logger.info(f"向量嵌入生成完成，成功: {len(valid_data)}/{len(data_batch)} 条")
        return valid_data
    
    def insert_data_batch(self, data_batch: List[Dict[str, Any]]) -> int:
        """批量插入数据，带重试机制"""
        max_retries = 3
        
        for attempt in range(max_retries):
            try:
                # 使用upsert方法插入数据
                self.memory_store.client.upsert(
                    table_name=self.memory_store.table_name,
                    data=data_batch
                )
                return len(data_batch)
                
            except Exception as e:
                logger.warning(f"第{attempt + 1}次尝试插入数据失败: {str(e)}")
                
                if "Lost connection" in str(e) or "connection" in str(e).lower():
                    # 数据库连接问题，尝试重连
                    try:
                        logger.info("检测到数据库连接问题，尝试重连...")
                        self.memory_store._reconnect()
                        logger.info("数据库重连成功")
                    except Exception as reconnect_e:
                        logger.error(f"数据库重连失败: {str(reconnect_e)}")
                
                if attempt < max_retries - 1:
                    time.sleep(2)  # 等待2秒后重试
                else:
                    raise
    
    def import_data(self, file_path: str):
        """导入数据的主函数"""
        start_time = time.time()
        
        try:
            # 发送开始导入的通知
            send_info_alarm(f"开始导入Excel数据: {file_path}")
            
            # 初始化模型和存储
            self.initialize_models()
            
            # 读取Excel文件
            df = self.read_excel(file_path)
            
            # 准备数据
            data_list = self.prepare_data(df)
            if not data_list:
                raise ValueError("没有有效的数据可以导入")
            
            # 分批处理向量嵌入
            embedding_batch_size = 10  # 每批处理10条数据生成嵌入
            all_processed_data = []
            
            for i in range(0, len(data_list), embedding_batch_size):
                batch = data_list[i:i + embedding_batch_size]
                logger.info(f"正在处理第{i//embedding_batch_size + 1}批向量嵌入，数量: {len(batch)}")
                
                try:
                    batch_with_embeddings = self.process_embeddings_batch(batch)
                    all_processed_data.extend(batch_with_embeddings)
                    
                    # 每批之间稍作停顿
                    time.sleep(0.5)
                    
                except Exception as e:
                    logger.error(f"第{i//embedding_batch_size + 1}批向量嵌入处理失败: {str(e)}")
                    continue
            
            if not all_processed_data:
                raise ValueError("没有成功生成向量嵌入的数据")
            
            logger.info(f"向量嵌入生成完成，最终数据量: {len(all_processed_data)}条")
            
            # 分批插入数据库
            insert_batch_size = 20  # 每批插入20条数据
            total_inserted = 0
            
            for i in range(0, len(all_processed_data), insert_batch_size):
                batch = all_processed_data[i:i + insert_batch_size]
                logger.info(f"正在插入第{i//insert_batch_size + 1}批数据，数量: {len(batch)}")
                
                try:
                    inserted_count = self.insert_data_batch(batch)
                    total_inserted += inserted_count
                    logger.info(f"第{i//insert_batch_size + 1}批数据插入成功，已插入: {total_inserted}/{len(all_processed_data)}")
                    
                    # 每批之间稍作停顿
                    time.sleep(0.1)
                    
                except Exception as e:
                    logger.error(f"第{i//insert_batch_size + 1}批数据插入失败: {str(e)}")
                    continue
            
            # 计算总耗时
            elapsed_time = time.time() - start_time
            
            # 生成导入报告
            report = f"""
数据导入完成！
- 原始数据: {len(df)}条
- 有效数据: {len(data_list)}条
- 成功生成嵌入: {len(all_processed_data)}条
- 成功插入数据库: {total_inserted}条
- 总耗时: {elapsed_time:.2f}秒
- 平均处理速度: {len(all_processed_data)/elapsed_time:.2f}条/秒
"""
            
            logger.info(report)
            send_info_alarm(f"Excel数据导入完成: 成功导入{total_inserted}条数据，耗时{elapsed_time:.2f}秒")
            
        except Exception as e:
            elapsed_time = time.time() - start_time
            error_msg = f"导入数据失败: {str(e)}，耗时: {elapsed_time:.2f}秒"
            logger.error(error_msg)
            
            import traceback
            logger.error(traceback.format_exc())
            
            send_error_alarm(error_msg, "Excel数据导入失败", traceback.format_exc())
            raise

def main():
    """主函数"""
    if len(sys.argv) != 2:
        print("用法: python import_excel_robust.py <excel_file_path>")
        sys.exit(1)
    
    file_path = sys.argv[1]
    
    # 创建导入器并执行导入
    importer = ExcelImporter()
    importer.import_data(file_path)

if __name__ == "__main__":
    main()
