# 数据导入工具

这个包提供了从 Excel 文件导入数据到 OceanBase 的 `agent_memory` 表的工具。

## 使用方法

### 直接运行导入脚本

```bash
python -m init.import_excel
```

这将读取项目根目录下的 `AgentMemory.xlsx` 文件，并将数据导入到数据库中。

### 使用命令行工具

```bash
# 导入指定的Excel文件
python -m init.cli import-excel --file /path/to/your/AgentMemory.xlsx
```

## Excel 文件格式

Excel 文件应包含以下列（表头）：

- `agentId`: Agent ID，默认为 "memory_agent"
- `domain`: 所属领域
- `memoryType`: 记忆类型
- `contentType`: 内容类型
- `topic`: 记忆话题（可选）
- `content`: 记忆内容（必需）
- `importance`: 重要程度，范围 0-1，默认为 0.5
- `confidence`: 可信度，范围 0-1，默认为 0.9
- `ttl`: 过期时间（可选）
- `relatedUserId`: 关联用户 ID（可选）
- `metadata`: 元数据，可以是 JSON 字符串或普通文本（可选）

## 注意事项

1. 导入过程会自动为每条记录生成雪花 ID
2. 导入过程会使用 OpenAI API 为每条记录生成向量嵌入
3. 如果数据量较大，导入过程可能需要一些时间
4. 确保 `config/settings.py` 中的数据库连接和 OpenAI API 配置正确
