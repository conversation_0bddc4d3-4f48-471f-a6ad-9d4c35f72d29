"""
命令行工具，用于执行各种初始化操作
"""
import argparse
import os
import logging
from init.import_excel_batch import import_data

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),  # 输出到控制台
    ]
)
logger = logging.getLogger("CLI")

def main():
    """命令行入口函数"""
    parser = argparse.ArgumentParser(description="记忆向量存储系统初始化工具")
    subparsers = parser.add_subparsers(dest="command", help="可用命令")

    # 导入Excel数据的命令
    import_parser = subparsers.add_parser("import-excel", help="从Excel文件导入数据")
    import_parser.add_argument("--file", "-f", required=True, help="Excel文件路径")
    import_parser.add_argument("--batch-size", "-b", type=int, default=100, help="批处理大小，默认为100")

    # 解析命令行参数
    args = parser.parse_args()

    if args.command == "import-excel":
        file_path = args.file
        if not os.path.exists(file_path):
            logger.error(f"文件不存在: {file_path}")
            return 1

        try:
            logger.info(f"开始从Excel文件导入数据: {file_path}")
            import_data(file_path)
            logger.info("数据导入完成")
            return 0
        except Exception as e:
            logger.error(f"数据导入失败: {str(e)}")
            return 1
    else:
        parser.print_help()
        return 0

if __name__ == "__main__":
    exit(main())
