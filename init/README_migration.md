# 数据迁移说明

## 概述

本迁移脚本用于将数据从旧表 `agent_memory_new`（使用L2距离）迁移到新表 `agent_memory`（使用余弦距离）。

## 主要变更

### 1. 表名变更
- **旧表**: `agent_memory_new`
- **新表**: `agent_memory`

### 2. 向量索引变更
- **旧索引**: L2距离 (`distance=l2`)
- **新索引**: 余弦距离 (`distance=cosine`)

### 3. 向量归一化
- 所有向量将被归一化到单位长度
- 这是使用余弦距离的前提条件

## 使用步骤

### 1. 创建新表
首先在数据库中创建新表：

```sql
CREATE TABLE agent_memory (
    id VARCHAR(32) PRIMARY KEY COMMENT '记忆ID，使用雪花算法生成',
    agent_id VARCHAR(64) NOT NULL DEFAULT 'memory_agent' COMMENT 'Agent ID',
    domain VARCHAR(100) NOT NULL DEFAULT '' COMMENT '所属领域',
    memory_type VARCHAR(50) NOT NULL DEFAULT '' COMMENT '记忆类型',
    content_type VARCHAR(50) NOT NULL DEFAULT '' COMMENT '内容类型',
    topic VARCHAR(255) DEFAULT NULL COMMENT '记忆话题',
    content TEXT NOT NULL COMMENT '记忆内容',
    embedding VECTOR(1024) NOT NULL COMMENT '内容的向量嵌入，1024维',
    topic_embedding VECTOR(1024) NOT NULL COMMENT '话题的向量嵌入，1024维',
    importance FLOAT NOT NULL DEFAULT 0.1 COMMENT '重要程度，默认为低重要性',
    confidence FLOAT NOT NULL DEFAULT 0.3 COMMENT '可信度，默认为低可信度',
    ttl VARCHAR(100) DEFAULT NULL COMMENT '过期时间（字符串格式）',
    related_user_id VARCHAR(64) DEFAULT NULL COMMENT '关联用户ID',
    meta_data TEXT DEFAULT NULL COMMENT '额外元数据（JSON字符串）',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    -- 双向量索引：使用HNSW算法 + 余弦距离
    VECTOR INDEX idx_memory_embedding_hnsw(embedding)
    WITH (distance=cosine, type=hnsw, lib=vsag),

    VECTOR INDEX idx_memory_topic_embedding_hnsw(topic_embedding)
    WITH (distance=cosine, type=hnsw, lib=vsag),

    -- 其他索引
    INDEX idx_agent_id (agent_id),
    INDEX idx_domain (domain),
    INDEX idx_ttl (ttl)
) COMMENT='Agent 双向量记忆表，支持内容和话题双向量搜索（使用余弦距离）';
```

### 2. 运行迁移脚本
```bash
cd /path/to/your/project
python init/migrate_to_cosine_table.py
```

### 3. 验证迁移结果
脚本会自动验证：
- 记录数量是否一致
- 向量是否正确归一化
- 数据完整性检查

### 4. 更新应用配置
迁移完成后，应用代码已自动更新：
- 表名从 `agent_memory_new` 改为 `agent_memory`
- 距离函数从 `l2_distance` 改为 `cosine_distance`
- 相似度计算适配余弦距离

## 迁移特性

### 向量归一化
- **目的**: 余弦距离要求向量归一化到单位长度
- **方法**: 每个向量除以其模长
- **验证**: 归一化后向量模长应为1.0（±1e-6误差）

### 批量处理
- **批次大小**: 100条记录/批次
- **内存优化**: 避免一次性加载所有数据
- **进度显示**: 实时显示迁移进度

### 错误处理
- **跳过错误记录**: 单条记录失败不影响整体迁移
- **详细日志**: 记录所有错误和警告信息
- **回滚机制**: 批次失败时自动回滚

## 性能影响

### 余弦距离 vs L2距离
- **余弦距离**: 更适合文本向量，关注方向而非大小
- **计算复杂度**: 略高于L2距离，但差异很小
- **搜索精度**: 对于归一化向量，通常更准确

### 预期改进
- **相似度计算**: 更合理的相似度得分范围
- **搜索质量**: 更好的语义相似度匹配
- **得分平衡**: 向量相似度与业务权重更好平衡

## 注意事项

### 1. 备份数据
迁移前建议备份原表：
```sql
CREATE TABLE agent_memory_new_backup AS SELECT * FROM agent_memory_new;
```

### 2. 停机时间
- 迁移期间建议停止写入操作
- 读取操作可以继续（从旧表）

### 3. 验证测试
迁移完成后建议进行功能测试：
- 搜索功能是否正常
- 相似度得分是否合理
- 性能是否满足要求

### 4. 清理工作
确认迁移成功后，可以删除旧表：
```sql
-- 谨慎操作，确保新表工作正常
-- DROP TABLE agent_memory_new;
```

## 故障排除

### 常见问题

1. **向量解析失败**
   - 检查向量数据格式
   - 确认向量维度正确

2. **归一化异常**
   - 检查是否存在零向量
   - 验证向量数值范围

3. **连接失败**
   - 检查数据库连接配置
   - 确认权限设置

### 日志文件
迁移过程会生成详细日志：
- **文件位置**: `migration.log`
- **内容**: 迁移进度、错误信息、性能统计

## 联系支持
如遇到问题，请查看日志文件并联系技术支持。
