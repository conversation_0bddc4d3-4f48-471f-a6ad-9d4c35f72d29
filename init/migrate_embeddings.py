#!/usr/bin/env python3
"""
向量嵌入迁移脚本
从agent_memory表查询数据，重新向量化content内容，更新embedding字段到agent_memory_new表
"""
import os
import sys
import logging
import time
import json
from typing import List, Dict, Any
from pyobvector import ObVecClient
from sqlalchemy import func, text
from langchain_openai import OpenAIEmbeddings

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.settings import connection_args, openai_config
from utils.snowflake import generate_id

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),  # 输出到控制台
    ]
)
logger = logging.getLogger("EmbeddingMigration")


class EmbeddingMigrator:
    def __init__(self):
        """初始化迁移器"""
        self.source_table = "agent_memory"
        self.target_table = "agent_memory_new"
        self.batch_size = 50  # 每批处理的记录数

        # 初始化数据库连接
        self._init_database()

        # 初始化嵌入模型
        self._init_embeddings()

    def _init_database(self):
        """初始化数据库连接"""
        try:
            logger.info("初始化数据库连接...")
            logger.info(f"连接到数据库: {connection_args['host']}:{connection_args['port']}, 数据库: {connection_args['db_name']}")

            self.client = ObVecClient(
                uri=f"{connection_args['host']}:{connection_args['port']}",
                user=connection_args['user'],
                password=connection_args['password'],
                db_name=connection_args['db_name']
            )

            # 设置HNSW搜索参数
            self.client.set_ob_hnsw_ef_search(64)
            logger.info("数据库连接成功")

        except Exception as e:
            logger.error(f"数据库连接失败: {str(e)}")
            raise

    def _init_embeddings(self):
        """初始化嵌入模型"""
        try:
            logger.info("初始化OpenAI Embeddings模型...")
            logger.info(f"模型配置: {openai_config['model']}, API Base: {openai_config['openai_api_base']}")

            self.embeddings = OpenAIEmbeddings(
                model=openai_config["model"],
                openai_api_key=openai_config["openai_api_key"],
                openai_api_base=openai_config["openai_api_base"],
                # 跳过上下文长度检查，直接发送文本
                check_embedding_ctx_length=False
            )
            logger.info("OpenAI Embeddings模型初始化成功")

        except Exception as e:
            logger.error(f"嵌入模型初始化失败: {str(e)}")
            raise

    def query_source_data(self, processed_ids: set = None) -> List[Dict[str, Any]]:
        """查询源表数据，一次性获取所有数据

        Args:
            processed_ids: 已处理的ID集合，用于过滤重复数据

        Returns:
            查询结果列表
        """
        try:
            logger.info(f"查询源表 {self.source_table} 的所有数据")

            # 使用ann_search查询所有数据，但设置一个很大的topk值
            results = self.client.ann_search(
                table_name=self.source_table,
                vec_data=[0] * 1536,  # 使用空向量，我们只关心数据查询
                vec_column_name="embedding",
                distance_func=func.l2_distance,
                topk=10000,  # 设置一个很大的值来获取所有数据
                output_column_names=[
                    "id", "agent_id", "domain", "memory_type", "content_type",
                    "topic", "content", "importance", "confidence", "ttl",
                    "related_user_id", "metadata", "created_at", "updated_at"
                ],
                with_dist=False
            )

            # 处理查询结果
            records = []
            for row in results.fetchall():
                record_id = row[0]

                # 过滤已处理的记录
                if processed_ids and record_id in processed_ids:
                    continue

                record = {
                    "id": record_id,
                    "agent_id": row[1],
                    "domain": row[2],
                    "memory_type": row[3],
                    "content_type": row[4],
                    "topic": row[5],
                    "content": row[6],
                    "importance": row[7],
                    "confidence": row[8],
                    "ttl": row[9],
                    "related_user_id": row[10],
                    "metadata": row[11],
                    "created_at": row[12],
                    "updated_at": row[13]
                }
                records.append(record)

            logger.info(f"查询到 {len(records)} 条记录")
            return records

        except Exception as e:
            logger.error(f"查询源表数据失败: {str(e)}")
            raise

    def generate_embeddings_batch(self, records: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """批量生成向量嵌入

        Args:
            records: 记录列表

        Returns:
            包含新向量嵌入的记录列表
        """
        logger.info(f"开始为 {len(records)} 条记录生成向量嵌入")

        updated_records = []
        total_embedding_time = 0

        for i, record in enumerate(records):
            try:
                content = record.get("content", "")
                if not content:
                    logger.warning(f"跳过空内容记录: {record['id']}")
                    continue

                # 生成向量嵌入
                embedding_start = time.time()
                embedding = self.embeddings.embed_query(content)
                embedding_time = time.time() - embedding_start
                total_embedding_time += embedding_time

                # 检查嵌入向量是否有效
                if not embedding or len(embedding) == 0:
                    logger.warning(f"跳过无效嵌入向量的记录: {record['id']}")
                    continue

                # 添加新的嵌入向量
                record["embedding"] = embedding
                updated_records.append(record)

                # 每处理10条记录打印一次进度
                if (i + 1) % 10 == 0 or (i + 1) == len(records):
                    avg_time = total_embedding_time / (i + 1)
                    logger.info(f"向量嵌入进度: {i + 1}/{len(records)}, "
                               f"平均耗时: {avg_time:.3f}秒/条")

            except Exception as e:
                logger.error(f"生成嵌入向量失败: {str(e)}, 记录ID: {record.get('id')}")
                continue

        logger.info(f"向量嵌入生成完成，成功: {len(updated_records)}/{len(records)} 条")
        return updated_records

    def insert_target_data(self, records: List[Dict[str, Any]]) -> int:
        """插入数据到目标表

        Args:
            records: 包含嵌入向量的记录列表

        Returns:
            成功插入的记录数
        """
        if not records:
            return 0

        try:
            logger.info(f"开始插入 {len(records)} 条记录到目标表 {self.target_table}")

            # 准备插入数据
            insert_data = []
            for record in records:
                # 为目标表生成新的ID以避免主键冲突
                new_id = generate_id()
                data = {
                    "id": new_id,  # 生成新ID
                    "agent_id": record["agent_id"],
                    "domain": record["domain"],
                    "memory_type": record["memory_type"],
                    "content_type": record["content_type"],
                    "topic": record["topic"],
                    "content": record["content"],
                    "embedding": record["embedding"],
                    "importance": record["importance"],
                    "confidence": record["confidence"],
                    "ttl": record["ttl"],
                    "related_user_id": record["related_user_id"],
                    "metadata": record["metadata"],
                    "created_at": record["created_at"],
                    "updated_at": record["updated_at"]
                }
                insert_data.append(data)

            # 批量插入数据，使用upsert以处理可能的重复
            insert_start = time.time()
            try:
                self.client.insert(
                    table_name=self.target_table,
                    data=insert_data
                )
            except Exception as insert_error:
                # 如果insert失败，尝试使用upsert
                if "Duplicate entry" in str(insert_error):
                    logger.warning(f"检测到主键冲突，尝试使用upsert方法")
                    self.client.upsert(
                        table_name=self.target_table,
                        data=insert_data
                    )
                else:
                    raise insert_error

            insert_time = time.time() - insert_start

            logger.info(f"成功插入 {len(insert_data)} 条记录到 {self.target_table}, "
                       f"插入耗时: {insert_time:.3f}秒")

            return len(insert_data)

        except Exception as e:
            logger.error(f"插入目标表数据失败: {str(e)}")
            raise

    def migrate_all_data(self):
        """迁移所有数据"""
        logger.info("开始向量嵌入迁移任务")
        start_time = time.time()

        total_processed = 0
        total_migrated = 0
        processed_ids = set()  # 记录已处理的ID

        try:
            # 一次性查询所有数据
            logger.info("查询源表所有数据...")
            all_records = self.query_source_data()

            if not all_records:
                logger.info("没有数据需要处理")
                return

            logger.info(f"总共查询到 {len(all_records)} 条记录，开始分批处理")

            # 分批处理数据
            for i in range(0, len(all_records), self.batch_size):
                batch_records = all_records[i:i + self.batch_size]
                batch_num = i // self.batch_size + 1

                logger.info(f"处理第 {batch_num} 批数据，包含 {len(batch_records)} 条记录")

                # 过滤已处理的记录
                unprocessed_records = []
                for record in batch_records:
                    if record["id"] not in processed_ids:
                        unprocessed_records.append(record)
                        processed_ids.add(record["id"])

                if not unprocessed_records:
                    logger.info(f"第 {batch_num} 批数据已全部处理，跳过")
                    continue

                total_processed += len(unprocessed_records)

                # 生成向量嵌入
                updated_records = self.generate_embeddings_batch(unprocessed_records)

                if updated_records:
                    # 插入到目标表
                    migrated_count = self.insert_target_data(updated_records)
                    total_migrated += migrated_count

                # 打印总体进度
                elapsed_time = time.time() - start_time
                progress_percent = (i + len(batch_records)) / len(all_records) * 100
                logger.info(f"总体进度: {progress_percent:.1f}% ({i + len(batch_records)}/{len(all_records)}), "
                           f"已处理 {total_processed} 条，已迁移 {total_migrated} 条，"
                           f"耗时: {elapsed_time:.2f}秒")

            # 迁移完成
            total_time = time.time() - start_time
            logger.info(f"向量嵌入迁移任务完成！")
            logger.info(f"源表总记录数: {len(all_records)} 条")
            logger.info(f"实际处理: {total_processed} 条记录")
            logger.info(f"成功迁移: {total_migrated} 条记录")
            logger.info(f"总耗时: {total_time:.2f}秒")
            if total_processed > 0:
                logger.info(f"平均处理速度: {total_processed / total_time:.2f} 条/秒")

        except Exception as e:
            logger.error(f"迁移任务失败: {str(e)}")
            raise
        finally:
            # 关闭数据库连接
            try:
                if hasattr(self, 'client') and self.client:
                    self.client.close()
                    logger.info("数据库连接已关闭")
            except:
                pass


def main():
    """主函数"""
    try:
        logger.info("=" * 60)
        logger.info("向量嵌入迁移脚本启动")
        logger.info("=" * 60)

        # 创建迁移器并执行迁移
        migrator = EmbeddingMigrator()
        migrator.migrate_all_data()

        logger.info("=" * 60)
        logger.info("向量嵌入迁移脚本执行完成")
        logger.info("=" * 60)

    except Exception as e:
        logger.error(f"迁移脚本执行失败: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        sys.exit(1)


if __name__ == "__main__":
    main()
