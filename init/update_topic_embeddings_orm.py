#!/usr/bin/env python3
"""
Topic向量化更新脚本 (使用SQLAlchemy ORM)
查询agent_memory_new表的全部数据，将topic字段重新向量化并更新到topic_embedding字段
"""
import os
import sys
import logging
import time
from typing import List, Dict, Any
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from langchain_openai import OpenAIEmbeddings

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.settings import connection_args, openai_config
from models.schemas import AgentMemoryModel

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),  # 输出到控制台
    ]
)
logger = logging.getLogger("TopicEmbeddingUpdaterORM")


class TopicEmbeddingUpdaterORM:
    def __init__(self):
        """初始化Topic向量更新器"""
        self.batch_size = 50  # 每批处理的记录数

        # 初始化数据库连接
        self._init_database()

        # 初始化嵌入模型
        self._init_embeddings()

    def _init_database(self):
        """初始化数据库连接"""
        try:
            logger.info("初始化数据库连接...")
            logger.info(f"连接到数据库: {connection_args['host']}:{connection_args['port']}, 数据库: {connection_args['db_name']}")

            # 构建连接字符串
            connection_string = (
                f"mysql+pymysql://{connection_args['user']}:{connection_args['password']}"
                f"@{connection_args['host']}:{connection_args['port']}/{connection_args['db_name']}"
            )

            # 创建引擎
            self.engine = create_engine(
                connection_string,
                pool_size=connection_args.get('pool_size', 10),
                max_overflow=connection_args.get('max_overflow', 20),
                pool_timeout=connection_args.get('pool_timeout', 30),
                pool_recycle=connection_args.get('pool_recycle', 3600),
                pool_pre_ping=connection_args.get('pool_pre_ping', True),
                echo=connection_args.get('echo', False)
            )

            # 创建会话工厂
            self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)

            # 测试连接
            with self.engine.connect() as conn:
                conn.execute(text("SELECT 1"))

            logger.info("数据库连接成功")

        except Exception as e:
            logger.error(f"数据库连接失败: {str(e)}")
            raise

    def _init_embeddings(self):
        """初始化嵌入模型"""
        try:
            logger.info("初始化OpenAI Embeddings模型...")
            logger.info(f"模型配置: {openai_config['model']}, API Base: {openai_config['openai_api_base']}")

            self.embeddings = OpenAIEmbeddings(
                model=openai_config["model"],
                openai_api_key=openai_config["openai_api_key"],
                openai_api_base=openai_config["openai_api_base"],
                # 跳过上下文长度检查，直接发送文本
                check_embedding_ctx_length=False
            )
            logger.info("OpenAI Embeddings模型初始化成功")

        except Exception as e:
            logger.error(f"嵌入模型初始化失败: {str(e)}")
            raise

    def get_session(self):
        """获取数据库会话"""
        return self.SessionLocal()

    def query_records_need_update(self) -> List[Dict[str, Any]]:
        """查询需要更新topic_embedding的记录

        Returns:
            需要更新的记录列表
        """
        try:
            logger.info("查询所有有topic的记录（将全部重新更新topic_embedding）")

            with self.get_session() as session:
                # 使用原生SQL查询，避免数据类型转换问题
                try:
                    query_sql = text("""
                        SELECT id, topic
                        FROM agent_memory_new
                        WHERE topic IS NOT NULL AND topic != ''
                        ORDER BY created_at
                    """)

                    results = session.execute(query_sql).fetchall()

                    records = []
                    for result in results:
                        records.append({
                            "id": result[0],  # id
                            "topic": result[1]  # topic
                        })

                except Exception as query_error:
                    logger.error(f"原生SQL查询失败: {str(query_error)}，尝试使用ORM查询")
                    # 如果原生SQL失败，尝试使用ORM查询
                    query = session.query(AgentMemoryModel.id, AgentMemoryModel.topic).filter(
                        AgentMemoryModel.topic.isnot(None),
                        AgentMemoryModel.topic != ''
                    ).order_by(AgentMemoryModel.created_at)

                    results = query.all()

                    records = []
                    for result in results:
                        records.append({
                            "id": result.id,
                            "topic": result.topic
                        })

                logger.info(f"查询完成，找到 {len(records)} 条有topic的记录（将全部重新更新）")
                return records

        except Exception as e:
            logger.error(f"查询数据失败: {str(e)}")
            raise

    def generate_topic_embedding(self, topic: str) -> List[float]:
        """生成topic的向量嵌入

        Args:
            topic: topic文本

        Returns:
            向量嵌入列表
        """
        try:
            if not topic or topic.strip() == "":
                return None

            # 生成向量嵌入
            embedding = self.embeddings.embed_query(topic.strip())

            # 检查嵌入向量是否有效
            if not embedding or len(embedding) == 0:
                logger.warning(f"生成的向量嵌入无效: {topic}")
                return None

            logger.debug(f"生成topic向量嵌入成功，维度: {len(embedding)}")
            return embedding

        except Exception as e:
            logger.error(f"生成topic向量嵌入失败: {topic}, 错误: {str(e)}")
            return None

    def update_topic_embeddings_batch(self, records: List[Dict[str, Any]]) -> int:
        """批量更新topic向量嵌入

        Args:
            records: 记录列表

        Returns:
            成功更新的记录数
        """
        updated_count = 0
        total_embedding_time = 0

        logger.info(f"开始批量更新 {len(records)} 条记录的topic向量嵌入")

        with self.get_session() as session:
            for i, record in enumerate(records):
                try:
                    topic = record.get("topic", "")
                    record_id = record.get("id", "")

                    if not topic:
                        logger.debug(f"跳过空topic记录: {record_id}")
                        continue

                    # 生成topic向量嵌入
                    embedding_start = time.time()
                    topic_embedding = self.generate_topic_embedding(topic)
                    embedding_time = time.time() - embedding_start
                    total_embedding_time += embedding_time

                    if topic_embedding is None:
                        logger.warning(f"跳过无效topic向量的记录: {record_id}")
                        continue

                    # 使用原生SQL更新，完全避免ORM的数据类型转换问题
                    try:
                        # 将向量转换为字符串格式
                        embedding_str = str(topic_embedding)

                        # 使用原生SQL直接更新topic_embedding字段
                        update_sql = text("""
                            UPDATE agent_memory_new
                            SET topic_embedding = :topic_embedding, updated_at = CURRENT_TIMESTAMP
                            WHERE id = :record_id
                        """)

                        result = session.execute(update_sql, {
                            'topic_embedding': embedding_str,
                            'record_id': str(record_id)
                        })

                        if result.rowcount > 0:
                            updated_count += 1

                            # 每10条记录提交一次
                            if updated_count % 10 == 0:
                                session.commit()
                                logger.debug(f"已提交 {updated_count} 条更新")
                        else:
                            logger.warning(f"未找到记录或更新失败: {record_id}")

                    except Exception as update_error:
                        logger.error(f"更新记录{record_id}时发生错误: {str(update_error)}")
                        # 回滚当前事务并继续处理下一条记录
                        try:
                            session.rollback()
                        except:
                            pass
                        continue

                    # 每处理10条记录打印一次进度
                    if (i + 1) % 10 == 0 or (i + 1) == len(records):
                        avg_time = total_embedding_time / (i + 1)
                        logger.info(f"向量更新进度: {i + 1}/{len(records)}, "
                                   f"已更新: {updated_count}, "
                                   f"平均耗时: {avg_time:.3f}秒/条")

                except Exception as e:
                    logger.error(f"更新记录失败: {record.get('id', 'unknown')}, 错误: {str(e)}")
                    # 回滚当前事务
                    session.rollback()
                    continue

            # 最终提交
            try:
                session.commit()
                logger.info(f"批量更新完成，成功更新 {updated_count}/{len(records)} 条记录")
            except Exception as e:
                logger.error(f"最终提交失败: {str(e)}")
                session.rollback()

        return updated_count

    def get_statistics(self) -> Dict[str, int]:
        """获取统计信息"""
        try:
            with self.get_session() as session:
                # 总记录数
                total_count = session.query(AgentMemoryModel).count()

                # 有topic的记录数
                topic_count = session.query(AgentMemoryModel).filter(
                    AgentMemoryModel.topic.isnot(None),
                    AgentMemoryModel.topic != ''
                ).count()

                # 有topic_embedding的记录数（直接使用原生SQL避免类型转换问题）
                try:
                    result = session.execute(text(
                        "SELECT COUNT(*) FROM agent_memory_new WHERE topic_embedding IS NOT NULL"
                    ))
                    topic_embedding_count = result.scalar() or 0
                except Exception as vector_query_error:
                    logger.warning(f"查询topic_embedding统计失败: {str(vector_query_error)}")
                    topic_embedding_count = 0

                # 需要更新的记录数（所有有topic的记录）
                need_update_count = topic_count  # 全部重新更新

                return {
                    "total_records": total_count,
                    "records_with_topic": topic_count,
                    "records_with_topic_embedding": topic_embedding_count,
                    "records_need_update": need_update_count
                }

        except Exception as e:
            logger.error(f"获取统计信息失败: {str(e)}")
            return {}

    def run_update(self):
        """执行完整的更新流程"""
        start_time = time.time()

        try:
            logger.info("开始Topic向量化更新任务")

            # 1. 获取统计信息
            logger.info("步骤1: 获取统计信息")
            stats = self.get_statistics()
            if stats:
                logger.info(f"数据库统计信息:")
                logger.info(f"  总记录数: {stats['total_records']}")
                logger.info(f"  有topic的记录数: {stats['records_with_topic']}")
                logger.info(f"  已有topic_embedding的记录数: {stats['records_with_topic_embedding']}")
                logger.info(f"  需要更新的记录数: {stats['records_need_update']}")

            # 2. 查询所有有topic的记录
            logger.info("步骤2: 查询所有有topic的记录（将全部重新更新）")
            records_to_update = self.query_records_need_update()

            if not records_to_update:
                logger.info("没有找到有topic的记录")
                return

            # 3. 分批处理
            logger.info(f"步骤3: 分批处理，批次大小: {self.batch_size}")
            total_updated = 0

            for i in range(0, len(records_to_update), self.batch_size):
                batch = records_to_update[i:i + self.batch_size]
                batch_num = i // self.batch_size + 1
                total_batches = (len(records_to_update) - 1) // self.batch_size + 1

                logger.info(f"处理批次 {batch_num}/{total_batches}, 记录数: {len(batch)}")

                batch_updated = self.update_topic_embeddings_batch(batch)
                total_updated += batch_updated

                # 批次间稍作停顿，避免请求频率过高
                if i + self.batch_size < len(records_to_update):
                    time.sleep(1)

            # 4. 完成统计
            total_time = time.time() - start_time
            logger.info(f"Topic向量化更新任务完成!")
            logger.info(f"总记录数: {len(records_to_update)}")
            logger.info(f"成功更新: {total_updated}")
            logger.info(f"失败记录: {len(records_to_update) - total_updated}")
            logger.info(f"总耗时: {total_time:.2f}秒")

            if total_updated > 0:
                avg_time = total_time / total_updated
                logger.info(f"平均处理时间: {avg_time:.3f}秒/条")

            # 5. 最终统计
            logger.info("步骤4: 获取最终统计信息")
            final_stats = self.get_statistics()
            if final_stats:
                logger.info(f"更新后统计信息:")
                logger.info(f"  已有topic_embedding的记录数: {final_stats['records_with_topic_embedding']}")
                logger.info(f"  仍需更新的记录数: {final_stats['records_need_update']}")

        except Exception as e:
            logger.error(f"Topic向量化更新任务失败: {str(e)}")
            raise

    def close(self):
        """关闭连接"""
        try:
            if hasattr(self, 'engine') and self.engine:
                self.engine.dispose()
                logger.info("数据库连接已关闭")
        except Exception as e:
            logger.error(f"关闭数据库连接失败: {str(e)}")


def main():
    """主函数"""
    updater = None
    try:
        # 创建更新器实例
        updater = TopicEmbeddingUpdaterORM()

        # 执行更新
        updater.run_update()

    except KeyboardInterrupt:
        logger.info("用户中断操作")
    except Exception as e:
        logger.error(f"程序执行失败: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        sys.exit(1)
    finally:
        if updater:
            updater.close()


if __name__ == "__main__":
    main()
