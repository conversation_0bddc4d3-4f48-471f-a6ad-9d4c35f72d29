"""
从Excel文件批量导入数据到OceanBase的agent_memory表
"""
import os
import pandas as pd
import logging
from langchain_openai import OpenAIEmbeddings
from typing import List, Dict, Any
import json

# 导入自定义模块
from utils.snowflake import generate_id
from db.memory_store import AgentMemoryStore
from config.settings import connection_args, openai_config

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),  # 输出到控制台
    ]
)
logger = logging.getLogger("ImportExcelBatch")

def read_excel(file_path: str) -> pd.DataFrame:
    """
    读取Excel文件

    Args:
        file_path: Excel文件路径

    Returns:
        DataFrame: 包含Excel数据的DataFrame
    """
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"文件不存在: {file_path}")

    logger.info(f"读取Excel文件: {file_path}")
    df = pd.read_excel(file_path)
    logger.info(f"成功读取{len(df)}行数据")

    return df

def prepare_data(df: pd.DataFrame) -> List[Dict[str, Any]]:
    """
    准备要插入的数据

    Args:
        df: 包含Excel数据的DataFrame

    Returns:
        List[Dict[str, Any]]: 准备好的数据列表
    """
    data_list = []

    # 安全获取值的函数，处理NaN值
    def safe_get(row, key, default=""):
        value = row.get(key, default)
        if pd.isna(value):  # 检查是否为NaN
            return default
        return value

    # 安全转换为浮点数
    def safe_float(value, default=0.0):
        if pd.isna(value):
            return default
        try:
            return float(value)
        except (ValueError, TypeError):
            return default

    # 处理每一行数据
    for _, row in df.iterrows():
        # 生成雪花ID
        memory_id = generate_id()

        # 准备数据字典
        data = {
            "id": memory_id,
            "agent_id": safe_get(row, "agentId", "memory_agent"),
            "domain": safe_get(row, "领域", ""),
            "memory_type": safe_get(row, "类型", ""),
            "content_type": safe_get(row, "contentType", ""),
            "topic": None if pd.isna(row.get("记忆话题")) else str(row.get("记忆话题")),
            "content": safe_get(row, "记忆内容", ""),  # 内容是必需的
            "importance": safe_float(row.get("重要度"), 0.5),
            "confidence": safe_float(row.get("可信度"), 0.9),
            "updated_at": row.get("更新日期"),
            "created_at": row.get("更新日期"),
            "ttl": None if pd.isna(row.get("时效性")) else str(row.get("时效性")),
            "related_user_id": None if pd.isna(row.get("relatedUserId")) else str(row.get("relatedUserId")),
        }

        # 处理metadata字段
        metadata = row.get("metadata")
        if pd.notna(metadata):
            # 如果metadata是字符串，尝试解析为JSON
            if isinstance(metadata, str):
                try:
                    metadata = json.loads(metadata)
                except json.JSONDecodeError:
                    # 如果解析失败，将其作为普通字符串处理
                    metadata = {"text": metadata}
            # 确保元数据中没有NaN值
            if isinstance(metadata, dict):
                for k, v in list(metadata.items()):
                    if pd.isna(v):
                        metadata[k] = None
            data["metadata"] = json.dumps(metadata, ensure_ascii=False)
        else:
            data["metadata"] = None

        data_list.append(data)

    logger.info(f"准备了{len(data_list)}条数据")
    return data_list

def generate_embeddings_batch(data_batch: List[Dict[str, Any]], embeddings_model) -> List[Dict[str, Any]]:
    """
    为一批数据生成向量嵌入

    Args:
        data_batch: 准备好的数据批次
        embeddings_model: 嵌入模型

    Returns:
        List[Dict[str, Any]]: 包含向量嵌入的数据列表
    """
    for i, data in enumerate(data_batch):
        try:
            content = data["content"]
            # 确保内容不为空
            if not content or pd.isna(content):
                logger.warning(f"跳过空内容记录: {data['id']}")
                continue

            # 生成内容的向量嵌入
            embedding = embeddings_model.embed_query(content)

            # 检查嵌入向量是否有效
            if embedding and not any(pd.isna(x) for x in embedding):
                data["embedding"] = embedding
            else:
                logger.warning(f"跳过无效嵌入向量的记录: {data['id']}")
                continue
        except Exception as e:
            logger.error(f"生成嵌入向量失败: {str(e)}, 记录ID: {data.get('id')}")
            continue

    # 过滤掉没有embedding字段的数据
    return [data for data in data_batch if "embedding" in data]

def import_data(file_path: str, batch_size: int = 10):
    """
    从Excel文件批量导入数据到OceanBase

    Args:
        file_path: Excel文件路径
        batch_size: 批处理大小，默认为100
    """
    try:
        # 读取Excel文件
        df = read_excel(file_path)

        # 准备数据
        data_list = prepare_data(df)
        total_records = len(data_list)

        # 初始化embedding模型
        logger.info("初始化OpenAI Embeddings模型")
        embeddings = OpenAIEmbeddings(
            model=openai_config["model"],
            openai_api_key=openai_config["openai_api_key"],
            openai_api_base=openai_config["openai_api_base"]
        )
        logger.info("OpenAI Embeddings模型初始化成功")

        # 初始化记忆存储
        logger.info(f"连接到数据库: {connection_args['host']}:{connection_args['port']}")
        memory_store = AgentMemoryStore(connection_args, embeddings)

        # 批量处理和插入数据
        logger.info(f"开始批量处理和插入数据，批大小: {batch_size}")

        processed_count = 0
        for i in range(0, total_records, batch_size):
            # 获取当前批次的数据
            batch_data = data_list[i:i + batch_size]
            batch_size_actual = len(batch_data)

            # 为当前批次生成向量嵌入
            logger.info(f"正在处理批次 {i//batch_size + 1}/{(total_records-1)//batch_size + 1}，共 {batch_size_actual} 条数据")
            batch_data = generate_embeddings_batch(batch_data, embeddings)

            # 插入当前批次的数据
            memory_store.client.insert(
                table_name=memory_store.table_name,
                data=batch_data
            )

            processed_count += batch_size_actual
            logger.info(f"批次 {i//batch_size + 1} 处理完成，已处理 {processed_count}/{total_records} 条数据")

        logger.info(f"成功导入 {processed_count} 条数据到OceanBase")

    except Exception as e:
        logger.error(f"导入数据失败: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        raise

if __name__ == "__main__":
    # 获取当前脚本所在目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    # 构建Excel文件路径
    # excel_file = "/Users/<USER>/PycharmProjects/memory-server-new/init/AgentMemory.xlsx"
    # 构建Excel文件路径
    excel_file = os.path.join(current_dir, "AgentMemory0520.xlsx")

    # 导入数据，使用批大小100
    import_data(excel_file, batch_size=10)
