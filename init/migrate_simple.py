#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版数据迁移脚本：从 agent_memory_new 迁移到 agent_memory
主要变更：
1. 表名从 agent_memory_new 改为 agent_memory
2. 向量索引从 L2 距离改为余弦距离
3. 对所有向量进行归一化处理

使用方法：
python init/migrate_simple.py
"""

import math
import json
import logging
from typing import List, Optional

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 数据库配置 - 请根据实际情况修改
DB_CONFIG = {
    "host": "************",
    "port": "2883",
    "user": "ebc_agent_memory_rw@ob_test_ebc#test_ttcluster_001",
    "password": "pZDxKYQNK7VB63R4e3YE",
    "db_name": "ebc_agent_memory"
}

class SimpleMigrator:
    """简化版向量数据迁移器"""
    
    def __init__(self):
        """初始化迁移器"""
        self.engine = None
        self.session = None
        self.batch_size = 50  # 批量处理大小
        
    def connect_database(self):
        """连接数据库"""
        try:
            connection_string = (
                f"mysql+pymysql://{DB_CONFIG['user']}:{DB_CONFIG['password']}"
                f"@{DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['db_name']}"
                f"?charset=utf8mb4"
            )
            
            self.engine = create_engine(
                connection_string,
                pool_size=5,
                max_overflow=10,
                pool_recycle=3600,
                pool_pre_ping=True,
                echo=False
            )
            
            Session = sessionmaker(bind=self.engine)
            self.session = Session()
            
            logger.info("数据库连接成功")
            return True
            
        except Exception as e:
            logger.error(f"数据库连接失败: {str(e)}")
            return False
    
    def normalize_vector(self, vector: List[float]) -> List[float]:
        """归一化向量到单位长度"""
        if not vector:
            return vector
            
        # 计算向量的模长
        magnitude = math.sqrt(sum(x * x for x in vector))
        
        # 避免除零错误
        if magnitude == 0:
            logger.warning("发现零向量，跳过归一化")
            return vector
            
        # 归一化
        normalized = [x / magnitude for x in vector]
        return normalized
    
    def parse_vector_string(self, vector_str: str) -> Optional[List[float]]:
        """解析向量字符串"""
        if not vector_str:
            return None
            
        try:
            # 尝试直接解析为JSON数组
            if vector_str.startswith('[') and vector_str.endswith(']'):
                return json.loads(vector_str)
            
            # 尝试解析为逗号分隔的数值
            if ',' in vector_str:
                return [float(x.strip()) for x in vector_str.split(',')]
            
            logger.warning(f"无法解析向量格式: {vector_str[:100]}...")
            return None
            
        except Exception as e:
            logger.error(f"向量解析失败: {str(e)}")
            return None
    
    def check_tables_exist(self) -> bool:
        """检查源表和目标表是否存在"""
        try:
            # 检查源表
            result = self.session.execute(text("SHOW TABLES LIKE 'agent_memory_new'"))
            if not result.fetchone():
                logger.error("源表 agent_memory_new 不存在")
                return False
            
            # 检查目标表
            result = self.session.execute(text("SHOW TABLES LIKE 'agent_memory'"))
            if not result.fetchone():
                logger.error("目标表 agent_memory 不存在，请先创建表")
                return False
                
            logger.info("源表和目标表都存在")
            return True
            
        except Exception as e:
            logger.error(f"检查表存在性失败: {str(e)}")
            return False
    
    def get_source_data_count(self) -> int:
        """获取源表数据总数"""
        try:
            result = self.session.execute(text("SELECT COUNT(*) FROM agent_memory_new"))
            count = result.scalar()
            logger.info(f"源表 agent_memory_new 共有 {count} 条记录")
            return count
        except Exception as e:
            logger.error(f"获取源表数据总数失败: {str(e)}")
            return 0
    
    def clear_target_table(self):
        """清空目标表（可选）"""
        try:
            result = self.session.execute(text("SELECT COUNT(*) FROM agent_memory"))
            existing_count = result.scalar()
            
            if existing_count > 0:
                response = input(f"目标表已有 {existing_count} 条记录，是否清空？(y/N): ")
                if response.lower() == 'y':
                    self.session.execute(text("DELETE FROM agent_memory"))
                    self.session.commit()
                    logger.info("目标表已清空")
                else:
                    logger.info("保留目标表现有数据")
                    
        except Exception as e:
            logger.error(f"清空目标表失败: {str(e)}")
    
    def migrate_batch(self, offset: int, limit: int) -> int:
        """迁移一批数据"""
        try:
            # 查询源数据
            query = text("""
                SELECT id, agent_id, domain, memory_type, content_type, topic, content,
                       embedding, topic_embedding, importance, confidence, ttl,
                       related_user_id, meta_data, created_at, updated_at
                FROM agent_memory_new
                ORDER BY id
                LIMIT :limit OFFSET :offset
            """)
            
            result = self.session.execute(query, {"limit": limit, "offset": offset})
            rows = result.fetchall()
            
            if not rows:
                return 0
            
            migrated_count = 0
            
            for row in rows:
                try:
                    # 解析和归一化embedding
                    embedding_str = row.embedding
                    if isinstance(embedding_str, str):
                        embedding = self.parse_vector_string(embedding_str)
                    else:
                        embedding = embedding_str
                    
                    if embedding:
                        embedding = self.normalize_vector(embedding)
                    else:
                        logger.warning(f"记录 {row.id} 的 embedding 为空，跳过")
                        continue
                    
                    # 解析和归一化topic_embedding
                    topic_embedding_str = row.topic_embedding
                    if isinstance(topic_embedding_str, str):
                        topic_embedding = self.parse_vector_string(topic_embedding_str)
                    else:
                        topic_embedding = topic_embedding_str
                    
                    if topic_embedding:
                        topic_embedding = self.normalize_vector(topic_embedding)
                    else:
                        logger.warning(f"记录 {row.id} 的 topic_embedding 为空，跳过")
                        continue
                    
                    # 插入到目标表
                    insert_query = text("""
                        INSERT INTO agent_memory 
                        (id, agent_id, domain, memory_type, content_type, topic, content,
                         embedding, topic_embedding, importance, confidence, ttl,
                         related_user_id, meta_data, created_at, updated_at)
                        VALUES 
                        (:id, :agent_id, :domain, :memory_type, :content_type, :topic, :content,
                         :embedding, :topic_embedding, :importance, :confidence, :ttl,
                         :related_user_id, :meta_data, :created_at, :updated_at)
                    """)
                    
                    self.session.execute(insert_query, {
                        "id": row.id,
                        "agent_id": row.agent_id,
                        "domain": row.domain,
                        "memory_type": row.memory_type,
                        "content_type": row.content_type,
                        "topic": row.topic,
                        "content": row.content,
                        "embedding": str(embedding),  # 转换为字符串存储
                        "topic_embedding": str(topic_embedding),
                        "importance": row.importance,
                        "confidence": row.confidence,
                        "ttl": row.ttl,
                        "related_user_id": row.related_user_id,
                        "meta_data": row.meta_data,
                        "created_at": row.created_at,
                        "updated_at": row.updated_at
                    })
                    
                    migrated_count += 1
                    
                except Exception as e:
                    logger.error(f"迁移记录 {row.id} 失败: {str(e)}")
                    continue
            
            # 提交批次
            self.session.commit()
            logger.info(f"成功迁移批次: offset={offset}, 迁移数量={migrated_count}/{len(rows)}")
            
            return migrated_count
            
        except Exception as e:
            logger.error(f"迁移批次失败: {str(e)}")
            self.session.rollback()
            return 0
    
    def verify_migration(self) -> bool:
        """验证迁移结果"""
        try:
            # 检查记录数量
            source_count = self.session.execute(text("SELECT COUNT(*) FROM agent_memory_new")).scalar()
            target_count = self.session.execute(text("SELECT COUNT(*) FROM agent_memory")).scalar()
            
            logger.info(f"迁移验证: 源表={source_count}条, 目标表={target_count}条")
            
            if target_count == 0:
                logger.error("目标表为空，迁移失败")
                return False
            
            # 抽样检查向量归一化
            sample_query = text("SELECT id, embedding, topic_embedding FROM agent_memory LIMIT 3")
            samples = self.session.execute(sample_query).fetchall()
            
            for sample in samples:
                # 检查embedding归一化
                if sample.embedding:
                    embedding = self.parse_vector_string(sample.embedding)
                    if embedding:
                        magnitude = math.sqrt(sum(x * x for x in embedding))
                        logger.info(f"记录 {sample.id} embedding 模长: {magnitude:.6f}")
                        if abs(magnitude - 1.0) > 1e-3:
                            logger.warning(f"记录 {sample.id} 的 embedding 未正确归一化: {magnitude}")
                
                # 检查topic_embedding归一化
                if sample.topic_embedding:
                    topic_embedding = self.parse_vector_string(sample.topic_embedding)
                    if topic_embedding:
                        magnitude = math.sqrt(sum(x * x for x in topic_embedding))
                        logger.info(f"记录 {sample.id} topic_embedding 模长: {magnitude:.6f}")
                        if abs(magnitude - 1.0) > 1e-3:
                            logger.warning(f"记录 {sample.id} 的 topic_embedding 未正确归一化: {magnitude}")
            
            logger.info("迁移验证完成")
            return True
            
        except Exception as e:
            logger.error(f"迁移验证失败: {str(e)}")
            return False
    
    def run_migration(self):
        """执行完整的迁移流程"""
        logger.info("开始数据迁移...")
        
        # 连接数据库
        if not self.connect_database():
            return False
        
        try:
            # 检查表存在性
            if not self.check_tables_exist():
                return False
            
            # 获取源数据总数
            total_count = self.get_source_data_count()
            if total_count == 0:
                logger.info("源表为空，无需迁移")
                return True
            
            # 清空目标表（可选）
            self.clear_target_table()
            
            # 批量迁移
            migrated_total = 0
            offset = 0
            
            while offset < total_count:
                migrated_count = self.migrate_batch(offset, self.batch_size)
                migrated_total += migrated_count
                offset += self.batch_size
                
                # 显示进度
                progress = min(offset, total_count) / total_count * 100
                logger.info(f"迁移进度: {progress:.1f}% ({migrated_total}/{total_count})")
            
            # 验证迁移结果
            if self.verify_migration():
                logger.info(f"数据迁移完成！共迁移 {migrated_total} 条记录")
                return True
            else:
                logger.error("迁移验证失败")
                return False
                
        except Exception as e:
            logger.error(f"迁移过程中发生错误: {str(e)}")
            return False
        
        finally:
            if self.session:
                self.session.close()
            if self.engine:
                self.engine.dispose()


def main():
    """主函数"""
    print("=" * 60)
    print("Agent Memory 简化版数据迁移脚本")
    print("从 agent_memory_new (L2距离) 迁移到 agent_memory (余弦距离)")
    print("=" * 60)
    
    # 显示数据库配置
    print(f"数据库配置:")
    print(f"  主机: {DB_CONFIG['host']}:{DB_CONFIG['port']}")
    print(f"  数据库: {DB_CONFIG['db_name']}")
    print(f"  用户: {DB_CONFIG['user']}")
    
    # 确认迁移
    response = input("\n确认开始迁移？这将对向量进行归一化处理 (y/N): ")
    if response.lower() != 'y':
        print("迁移已取消")
        return
    
    # 执行迁移
    migrator = SimpleMigrator()
    success = migrator.run_migration()
    
    if success:
        print("\n✅ 迁移成功完成！")
        print("\n注意事项：")
        print("1. 所有向量已归一化到单位长度")
        print("2. 新表使用余弦距离索引")
        print("3. 应用代码已更新为使用新表")
        print("4. 建议备份原表 agent_memory_new")
    else:
        print("\n❌ 迁移失败，请检查日志信息")


if __name__ == "__main__":
    main()
