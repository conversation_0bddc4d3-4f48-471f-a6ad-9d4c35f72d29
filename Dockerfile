FROM python:3.13

WORKDIR /app

# 设置环境变量
ENV ENV=production
ENV PORT=8000
ENV HEALTH_PORT=9195
ENV PYTHONPATH=/app
ENV SERVICE_NAME=memory-server
# 告警配置（需要在运行时设置）
# ENV ALARM_FS_WEBHOOK=
# ENV ALARM_FS_SECRET=

# 复制需求文件并安装依赖
COPY requirements.txt .
RUN pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple && \
    pip install --no-cache-dir -r requirements.txt

# 创建日志目录
RUN mkdir -p /data/log/memory-server

# 复制应用代码
COPY api /app/api
COPY core /app/core
COPY db /app/db
COPY models /app/models
COPY utils /app/utils
COPY config /app/config
COPY tests /app/tests
COPY main.py /app/
COPY supervisord.conf /app/
COPY start.sh /app/

# 设置执行权限
RUN chmod +x /app/start.sh

# 暴露端口
EXPOSE ${PORT} ${HEALTH_PORT}

# 使用启动脚本
CMD ["/app/start.sh"]
