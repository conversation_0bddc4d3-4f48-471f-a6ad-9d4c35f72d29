import os
import json
from typing import Dict, Any

# 获取当前环境
ENV = os.environ.get("APP_ENV", "dev")

# 基础配置
base_config = {
    # 服务配置
    "service_config": {
        "host": "0.0.0.0",
        "port": 8000,
        "health_port": 9195  # 健康检查端口
    }
}

# 开发环境配置
dev_config = {
    "connection_args": {
        "host": "************",
        "port": "2883",
        "user": "ebc_agent_memory_rw@ob_test_ebc#test_ttcluster_001",
        "password": "pZDxKYQNK7VB63R4e3YE",
        "db_name": "ebc_agent_memory",
        # SQLAlchemy连接池配置
        "pool_size": 10,           # 连接池大小
        "max_overflow": 20,        # 最大溢出连接数
        "pool_timeout": 30,        # 连接超时时间（秒）
        "pool_recycle": 3600,      # 连接回收时间（秒），1小时
        "pool_pre_ping": True,     # 连接前检查连接是否有效
        "echo": True              # 是否打印SQL语句
    },

    # OpenAI API配置
    "openai_config": {
        "model": "bge-large-zh-v1.5",  # embedding模型名
        "openai_api_key": "yuangongzhushou153:87510cbbb68b5645e2633824cb2cd026",   # API密钥
        "openai_api_base": "https://aigc-backend.skyengine.com.cn/eliza/v1"  # embedding服务baseurl
    },

    # 告警配置
    "alarm_config": {
        "fs_webhook": "https://open.feishu.cn/open-apis/bot/v2/hook/5a412abe-ac5d-4649-acea-688dacae9c09",
        "fs_secret": "TWj7vWuhPsZA6oZgM7lSvd",  # 飞书机器人密钥
        "service_name": "memory-server"  # 服务名称
    }
}

# 测试环境配置
test_config = {
    "connection_args": {
        "host": "************",
        "port": "2883",
        "user": "ebc_agent_memory_rw@ob_test_ebc#test_ttcluster_001",
        "password": "pZDxKYQNK7VB63R4e3YE",
        "db_name": "ebc_agent_memory",
        # SQLAlchemy连接池配置
        "pool_size": 10,           # 连接池大小
        "max_overflow": 20,        # 最大溢出连接数
        "pool_timeout": 30,        # 连接超时时间（秒）
        "pool_recycle": 3600,      # 连接回收时间（秒），1小时
        "pool_pre_ping": True,     # 连接前检查连接是否有效
        "echo": True              # 是否打印SQL语句
    },

    # OpenAI API配置
    "openai_config": {
        "model": "bge-large-zh-v1.5",  # embedding模型名
        "openai_api_key": "yuangongzhushou153:87510cbbb68b5645e2633824cb2cd026",  # API密钥
        "openai_api_base": "https://aigc-backend.skyengine.com.cn/eliza/v1"  # embedding服务baseurl
    },

    # 告警配置
    "alarm_config": {
        "fs_webhook": "https://open.feishu.cn/open-apis/bot/v2/hook/5a412abe-ac5d-4649-acea-688dacae9c09",
        "fs_secret": "TWj7vWuhPsZA6oZgM7lSvd",  # 飞书机器人密钥
        "service_name": "memory-server"  # 服务名称
    }
}

# 生产环境配置
prod_config = {
    "connection_args": {
        "host": "************",
        "port": "2883",
        "user": "ebc_agent_memory_rw@ob_test_ebc#test_ttcluster_001",
        "password": "pZDxKYQNK7VB63R4e3YE",
        "db_name": "ebc_agent_memory",
        # SQLAlchemy连接池配置
        "pool_size": 15,           # 生产环境适当增大连接池
        "max_overflow": 30,        # 最大溢出连接数
        "pool_timeout": 30,        # 连接超时时间（秒）
        "pool_recycle": 3600,      # 连接回收时间（秒），1小时
        "pool_pre_ping": True,     # 连接前检查连接是否有效
        "echo": True              # 生产环境不打印SQL
    },

    # OpenAI API配置
    "openai_config": {
        "model": "bge-large-zh-v1.5",  # embedding模型名
        "openai_api_key": "yuangongzhushou153:87510cbbb68b5645e2633824cb2cd026",  # API密钥
        "openai_api_base": "https://aigc-backend.skyengine.com.cn/eliza/v1"  # embedding服务baseurl
    },

    # 告警配置
    "alarm_config": {
        "fs_webhook": "https://open.feishu.cn/open-apis/bot/v2/hook/5a412abe-ac5d-4649-acea-688dacae9c09",  # 飞书机器人 webhook地址
        "fs_secret": "TWj7vWuhPsZA6oZgM7lSvd",   # 飞书机器人密钥
        "service_name": "memory-server"  # 服务名称
    }
}

# 环境配置映射
env_configs = {
    "dev": dev_config,
    "development": dev_config,
    "test": test_config,
    "testing": test_config,
    "prod": prod_config,
    "production": prod_config
}

# 从配置文件加载配置
def load_config_file() -> Dict[str, Any]:
    """从配置文件加载配置"""
    config_file = os.environ.get("CONFIG_FILE")
    if not config_file:
        return {}

    try:
        with open(config_file, "r") as f:
            return json.load(f)
    except Exception as e:
        print(f"加载配置文件失败: {str(e)}")
        return {}

# 合并配置
def merge_configs(base: Dict[str, Any], override: Dict[str, Any]) -> Dict[str, Any]:
    """合并配置"""
    result = base.copy()

    for key, value in override.items():
        if isinstance(value, dict) and key in result and isinstance(result[key], dict):
            # 递归合并嵌套字典
            result[key] = merge_configs(result[key], value)
        else:
            # 直接覆盖
            result[key] = value

    return result

# 获取最终配置
def get_config() -> Dict[str, Any]:
    """获取最终配置"""
    # 基础配置
    config = base_config.copy()

    # 合并环境特定配置
    if ENV in env_configs:
        config = merge_configs(config, env_configs[ENV])
    else:
        # 如果环境不匹配，记录警告并使用默认环境
        print(f"[WARNING] 未知环境: {ENV}，可用环境: {list(env_configs.keys())}，使用默认开发环境")
        config = merge_configs(config, env_configs["dev"])

    # 合并配置文件
    file_config = load_config_file()
    if file_config:
        config = merge_configs(config, file_config)

    return config

# 导出配置
config = get_config()
connection_args = config.get("connection_args", {})
openai_config = config.get("openai_config", {})
service_config = config.get("service_config", {})
alarm_config = config.get("alarm_config", {})
