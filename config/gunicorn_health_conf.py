# 健康检查服务的Gunicorn配置文件

# 绑定的IP和端口
bind = "0.0.0.0:9195"

# 工作进程数
workers = 2  # 健康检查服务可以使用较少的工作进程
worker_class = "uvicorn.workers.UvicornWorker"  # 使用uvicorn作为worker

# 超时设置
timeout = 30  # 30秒超时
graceful_timeout = 10  # 优雅关闭超时

# 日志设置 - 只输出到控制台
loglevel = "info"
accesslog = "-"  # 输出到标准输出
errorlog = "-"  # 输出到标准错误
capture_output = True  # 捕获标准输出和标准错误

# 进程名称
proc_name = "health-check"

# 预加载应用
preload_app = True

# 守护进程模式（在Docker中应该设置为False）
daemon = False

# 保持连接
keepalive = 2  # 2秒

# 最大请求数
max_requests = 1000
max_requests_jitter = 50  # 添加随机抖动，避免所有worker同时重启
