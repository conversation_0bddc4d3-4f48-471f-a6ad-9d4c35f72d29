# Gunicorn配置文件

# 绑定的IP和端口
bind = "0.0.0.0:8000"

# 工作进程数
workers = 4  # 一般建议设置为 (2 x $num_cores) + 1
worker_class = "uvicorn.workers.UvicornWorker"  # 使用uvicorn作为worker

# 超时设置
timeout = 120  # 120秒超时
graceful_timeout = 30  # 优雅关闭超时

# 日志设置 - 确保所有环境都输出到控制台
loglevel = "info"
accesslog = "-"  # 输出到标准输出
errorlog = "-"  # 输出到标准错误
capture_output = True  # 捕获应用的标准输出
enable_stdio_inheritance = True  # 继承标准输入输出

# 访问日志格式
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(D)s'

# 不使用logconfig_dict，让应用自己管理日志
# logconfig_dict = None

# 进程名称
proc_name = "memory-server"

# 预加载应用
preload_app = True

# 守护进程模式（在Docker中应该设置为False）
daemon = False

# 保持连接
keepalive = 5  # 5秒

# 最大请求数
max_requests = 1000
max_requests_jitter = 50  # 添加随机抖动，避免所有worker同时重启
