from pydantic import BaseModel, Field, field_validator
from typing import Dict, Any, Optional, Union, List, Literal
from datetime import datetime
from sqlalchemy import Column, String, Text, Float, DateTime, func, Index
from sqlalchemy.ext.declarative import declarative_base
from pyobvector import VECTOR  # 使用pyobvector提供的VECTOR类型

# SQLAlchemy Base
Base = declarative_base()

# SQLAlchemy模型定义
class AgentMemoryModel(Base):
    """Agent记忆数据库模型"""
    __tablename__ = 'agent_memory_new'

    id = Column(String(32), primary_key=True, comment='记忆ID，使用雪花算法生成')
    agent_id = Column(String(64), nullable=False, default='memory_agent', comment='Agent ID')
    domain = Column(String(100), nullable=False, default='', comment='所属领域')
    memory_type = Column(String(50), nullable=False, default='', comment='记忆类型')
    content_type = Column(String(50), nullable=False, default='', comment='内容类型')
    topic = Column(String(255), nullable=True, comment='记忆话题')
    content = Column(Text, nullable=False, comment='记忆内容')
    # 使用pyobvector提供的VECTOR类型，原生支持OceanBase的VECTOR字段
    embedding = Column(VECTOR(1024), nullable=False, comment='内容的向量嵌入')
    topic_embedding = Column(VECTOR(1024), nullable=False, comment='topic的向量嵌入')
    importance = Column(Float, nullable=False, default=0.5, comment='重要程度')
    confidence = Column(Float, nullable=False, default=0.9, comment='可信度')
    ttl = Column(String(100), nullable=True, comment='过期时间')
    related_user_id = Column(String(64), nullable=True, comment='关联用户ID')
    meta_data = Column(Text, nullable=True, comment='额外元数据')
    created_at = Column(DateTime, default=func.current_timestamp(), comment='创建时间')
    updated_at = Column(DateTime, default=func.current_timestamp(), onupdate=func.current_timestamp(), comment='更新时间')

    # 索引定义
    __table_args__ = (
        Index('idx_agent_id', 'agent_id'),
        Index('idx_domain', 'domain'),
        Index('idx_ttl', 'ttl'),
        # 向量索引需要在数据库层面创建，这里不定义
        {'comment': 'Agent 向量记忆表，用于存储Agent的记忆内容及向量'}
    )

# 定义Pydantic数据模型

class AgentMemory(BaseModel):
    """Agent记忆模型"""
    agentId: str = "memory_agent"
    domain: str
    memoryType: str
    contentType: str
    topic: Optional[str] = None
    content: str
    importance: float = 0.5
    confidence: float = 0.9
    ttl: Optional[datetime] = None
    relatedUserId: Optional[str] = None
    metadata: Union[Dict[str, Any], str] = Field(default_factory=dict)

class QueryRequest(BaseModel):
    """查询请求模型"""
    query: Optional[str] = None
    k: int = 4
    filters: Optional[Dict[str, Any]] = None
    importanceWeight: float = 0.01
    confidenceWeight: float = 0.2
    recencyWeight: float = 0.0
    contentWeights: float = 1.0
    topicWeights: float = 1.0

    @field_validator('k')
    def validate_k(cls, v):
        if v <= 0:
            raise ValueError('k must be positive')
        return v

    @field_validator('importanceWeight', 'confidenceWeight', 'recencyWeight', 'contentWeights', 'topicWeights')
    def validate_weights(cls, v):
        if v < 0 or v > 1:
            raise ValueError('weight must be between 0 and 1')
        return v

class DeleteRequest(BaseModel):
    """删除请求模型"""
    filterDict: Dict[str, Any]



class UpdateMemoryByIdRequest(BaseModel):
    """根据ID更新记忆请求模型"""
    newData: Dict[str, Any]

class BatchUpdateMemoriesByIdsRequest(BaseModel):
    """批量根据ID更新记忆请求模型"""
    items: List[Dict[str, Any]]  # 每个元素包含 id 和 newData

class BatchDeleteMemoriesByIdsRequest(BaseModel):
    """批量根据ID删除记忆请求模型"""
    ids: List[str]


class MemoryOperation(BaseModel):
    """记忆操作模型，用于综合接口"""
    operation: Literal["create", "update", "delete"]
    # 批量创建时使用
    memories: Optional[List[Union[AgentMemory, Dict[str, Any]]]] = None

    # 批量更新时使用
    updates: Optional[List[Dict[str, Any]]] = None  # 每个元素包含 id 和 newData

    # 批量删除时使用
    memory_ids: Optional[List[str]] = None  # 要删除的ID列表
    filter_dict: Optional[Dict[str, Any]] = None  # 删除操作可以提供过滤条件

    # 兼容单条操作
    memory_id: Optional[str] = None  # 单条更新或删除操作的ID
    memory_data: Optional[Union[AgentMemory, Dict[str, Any]]] = None  # 单条创建或更新操作的数据


class BatchMemoryOperationsRequest(BaseModel):
    """批量记忆操作请求模型，用于综合接口"""
    operations: List[MemoryOperation]
