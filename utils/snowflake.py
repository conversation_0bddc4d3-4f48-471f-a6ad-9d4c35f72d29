import time
import random
import threading

class SnowflakeIDGenerator:
    """
    雪花ID生成器
    
    生成64位的雪花ID:
    - 1位符号位，始终为0
    - 41位时间戳（毫秒级）
    - 10位工作机器ID（5位数据中心ID + 5位机器ID）
    - 12位序列号（毫秒内的计数）
    
    支持每毫秒生成4096个不同的ID
    """
    
    def __init__(self, datacenter_id=1, worker_id=1):
        """
        初始化雪花ID生成器
        
        Args:
            datacenter_id: 数据中心ID (0-31)
            worker_id: 工作机器ID (0-31)
        """
        # 起始时间戳（2020-01-01 00:00:00 UTC）
        self.twepoch = 1577836800000
        
        # 位长度
        self.worker_id_bits = 5
        self.datacenter_id_bits = 5
        self.sequence_bits = 12
        
        # 最大值
        self.max_worker_id = -1 ^ (-1 << self.worker_id_bits)
        self.max_datacenter_id = -1 ^ (-1 << self.datacenter_id_bits)
        
        # 移位
        self.worker_id_shift = self.sequence_bits
        self.datacenter_id_shift = self.sequence_bits + self.worker_id_bits
        self.timestamp_shift = self.sequence_bits + self.worker_id_bits + self.datacenter_id_bits
        
        # 序列掩码
        self.sequence_mask = -1 ^ (-1 << self.sequence_bits)
        
        # 参数校验
        if worker_id > self.max_worker_id or worker_id < 0:
            raise ValueError(f"worker_id不能大于{self.max_worker_id}或小于0")
        if datacenter_id > self.max_datacenter_id or datacenter_id < 0:
            raise ValueError(f"datacenter_id不能大于{self.max_datacenter_id}或小于0")
        
        self.worker_id = worker_id
        self.datacenter_id = datacenter_id
        self.sequence = 0
        self.last_timestamp = -1
        
        # 线程锁
        self._lock = threading.Lock()
    
    def _next_millis(self, last_timestamp):
        """
        获取下一毫秒时间戳
        """
        timestamp = self._get_timestamp()
        while timestamp <= last_timestamp:
            timestamp = self._get_timestamp()
        return timestamp
    
    def _get_timestamp(self):
        """
        获取当前毫秒时间戳
        """
        return int(time.time() * 1000)
    
    def generate_id(self):
        """
        生成雪花ID
        
        Returns:
            雪花ID字符串
        """
        with self._lock:
            timestamp = self._get_timestamp()
            
            # 时钟回拨处理
            if timestamp < self.last_timestamp:
                # 简单处理：等待时钟追上
                time.sleep((self.last_timestamp - timestamp) / 1000.0)
                timestamp = self._get_timestamp()
            
            # 同一毫秒内序列号递增
            if timestamp == self.last_timestamp:
                self.sequence = (self.sequence + 1) & self.sequence_mask
                # 同一毫秒内序列号用完
                if self.sequence == 0:
                    timestamp = self._next_millis(self.last_timestamp)
            else:
                # 不同毫秒内，序列号重置
                # 为了避免不同服务器时间相同时生成重复ID，添加随机起始序列号
                self.sequence = random.randint(0, 10)
            
            self.last_timestamp = timestamp
            
            # 生成ID
            snowflake_id = ((timestamp - self.twepoch) << self.timestamp_shift) | \
                          (self.datacenter_id << self.datacenter_id_shift) | \
                          (self.worker_id << self.worker_id_shift) | \
                          self.sequence
            
            return str(snowflake_id)

# 创建一个全局的ID生成器实例
# 可以根据需要调整数据中心ID和工作机器ID
id_generator = SnowflakeIDGenerator(datacenter_id=1, worker_id=1)

def generate_id():
    """
    生成雪花ID的便捷函数
    
    Returns:
        雪花ID字符串
    """
    return id_generator.generate_id()
