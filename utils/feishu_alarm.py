"""
飞书告警工具类
"""
import os
import time
import hmac
import base64
import hashlib
import json
import logging
import requests
from typing import Optional
from concurrent.futures import ThreadPoolExecutor
import threading

logger = logging.getLogger("FeiShuAlarm")

class FeiShuAlarmUtil:
    """飞书告警工具类"""

    TAG = "【记忆向量存储服务告警 服务({})】"

    def __init__(self):
        """初始化飞书告警工具"""
        # 从配置文件获取配置
        try:
            from config.settings import alarm_config
            self.fs_webhook = alarm_config.get("fs_webhook", "")
            self.fs_secret = alarm_config.get("fs_secret", "")
            self.server_name = alarm_config.get("service_name", "memory-server")
        except ImportError:
            # 如果无法导入配置，则从环境变量获取
            self.fs_webhook = os.environ.get("ALARM_FS_WEBHOOK", "")
            self.fs_secret = os.environ.get("ALARM_FS_SECRET", "")
            self.server_name = os.environ.get("SERVICE_NAME", "memory-server")

        # 创建线程池
        self.executor = ThreadPoolExecutor(max_workers=2, thread_name_prefix="feiShuExecutor")

        # 线程锁，用于防止重复告警
        self._lock = threading.Lock()
        self._last_alarm_time = {}
        self._alarm_interval = 300  # 5分钟内相同告警只发送一次

    def gen_sign(self, secret: str, timestamp: int) -> Optional[str]:
        """
        生成飞书签名

        Args:
            secret: 飞书机器人密钥
            timestamp: 时间戳

        Returns:
            签名字符串
        """
        try:
            # 拼接timestamp和secret
            string_to_sign = f"{timestamp}\n{secret}"

            # 使用HmacSHA256算法计算签名
            hmac_code = hmac.new(
                string_to_sign.encode("utf-8"),
                digestmod=hashlib.sha256
            ).digest()

            # base64编码
            sign = base64.b64encode(hmac_code).decode('utf-8')
            return sign
        except Exception as e:
            logger.error(f"生成飞书签名失败: {str(e)}")
            return None

    def _should_send_alarm(self, msg_key: str) -> bool:
        """
        检查是否应该发送告警（防止重复告警）

        Args:
            msg_key: 消息的唯一标识

        Returns:
            是否应该发送告警
        """
        with self._lock:
            current_time = time.time()
            last_time = self._last_alarm_time.get(msg_key, 0)

            if current_time - last_time >= self._alarm_interval:
                self._last_alarm_time[msg_key] = current_time
                return True
            return False

    def send_msg(self, msg: str, alarm_type: str = "ERROR") -> None:
        """
        发送飞书告警消息

        Args:
            msg: 告警消息内容
            alarm_type: 告警类型（ERROR, WARNING, INFO）
        """
        if not self.fs_webhook or not self.fs_secret:
            logger.error("飞书告警fsWebHook或fsSecret未配置")
            return

        # 生成消息唯一标识，用于防重复
        msg_key = f"{alarm_type}:{hash(msg) % 10000}"

        # 检查是否应该发送告警
        if not self._should_send_alarm(msg_key):
            logger.debug(f"告警消息重复，跳过发送: {msg_key}")
            return

        timestamp = int(time.time())
        sign = self.gen_sign(self.fs_secret, timestamp)

        if not sign:
            logger.error("生成飞书签名失败")
            return

        # 异步发送告警
        self.executor.submit(self._send_msg_async, msg, timestamp, sign, alarm_type)

    def _send_msg_async(self, msg: str, timestamp: int, sign: str, alarm_type: str) -> None:
        """
        异步发送告警消息

        Args:
            msg: 告警消息内容
            timestamp: 时间戳
            sign: 签名
            alarm_type: 告警类型
        """
        try:
            # 根据告警类型添加emoji
            emoji_map = {
                "ERROR": "🚨",
                "WARNING": "⚠️",
                "INFO": "ℹ️"
            }
            emoji = emoji_map.get(alarm_type, "📢")

            # 构造消息内容
            formatted_msg = f"{emoji} {self.TAG.format(self.server_name)}\n\n{msg}\n\n时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime())}"

            # 构造请求参数
            params = {
                "timestamp": str(timestamp),
                "sign": sign,
                "msg_type": "text",
                "content": {
                    "text": formatted_msg
                }
            }

            # 发送HTTP请求
            headers = {
                "Content-Type": "application/json;charset=utf-8"
            }

            response = requests.post(
                self.fs_webhook,
                headers=headers,
                data=json.dumps(params),
                timeout=10
            )

            logger.info(f"飞书告警发送完成，响应: {response.text}")

        except Exception as e:
            logger.error(f"发送飞书告警失败: {str(e)}")

    def send_error_alarm(self, error_msg: str, error_type: str = "", stack_trace: str = "") -> None:
        """
        发送错误告警

        Args:
            error_msg: 错误消息
            error_type: 错误类型
            stack_trace: 堆栈跟踪
        """
        msg_parts = [f"错误类型: {error_type}" if error_type else "", f"错误信息: {error_msg}"]

        if stack_trace:
            # 截取堆栈跟踪的前500个字符，避免消息过长
            truncated_trace = stack_trace[:500] + "..." if len(stack_trace) > 500 else stack_trace
            msg_parts.append(f"堆栈跟踪: {truncated_trace}")

        msg = "\n".join(filter(None, msg_parts))
        self.send_msg(msg, "ERROR")

    def send_warning_alarm(self, warning_msg: str) -> None:
        """
        发送警告告警

        Args:
            warning_msg: 警告消息
        """
        self.send_msg(f"警告信息: {warning_msg}", "WARNING")

    def send_info_alarm(self, info_msg: str) -> None:
        """
        发送信息告警

        Args:
            info_msg: 信息消息
        """
        self.send_msg(f"信息: {info_msg}", "INFO")

    def send_db_connection_alarm(self, error_msg: str, retry_count: int = 0) -> None:
        """
        发送数据库连接告警

        Args:
            error_msg: 错误消息
            retry_count: 重试次数
        """
        msg = f"数据库连接异常\n错误信息: {error_msg}"
        if retry_count > 0:
            msg += f"\n重试次数: {retry_count}"

        self.send_msg(msg, "ERROR")

    def send_service_start_alarm(self) -> None:
        """发送服务启动告警"""
        self.send_msg("服务启动成功", "INFO")

    def send_service_stop_alarm(self) -> None:
        """发送服务停止告警"""
        self.send_msg("服务停止", "WARNING")

    def close(self) -> None:
        """关闭线程池"""
        self.executor.shutdown(wait=True)

# 创建全局实例
feishu_alarm = FeiShuAlarmUtil()

# 便捷函数
def send_error_alarm(error_msg: str, error_type: str = "", stack_trace: str = "") -> None:
    """发送错误告警的便捷函数"""
    feishu_alarm.send_error_alarm(error_msg, error_type, stack_trace)

def send_warning_alarm(warning_msg: str) -> None:
    """发送警告告警的便捷函数"""
    feishu_alarm.send_warning_alarm(warning_msg)

def send_info_alarm(info_msg: str) -> None:
    """发送信息告警的便捷函数"""
    feishu_alarm.send_info_alarm(info_msg)

def send_db_connection_alarm(error_msg: str, retry_count: int = 0) -> None:
    """发送数据库连接告警的便捷函数"""
    feishu_alarm.send_db_connection_alarm(error_msg, retry_count)
