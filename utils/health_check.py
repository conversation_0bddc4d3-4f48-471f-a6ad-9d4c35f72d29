"""
数据库连接健康检查工具
"""
import logging
import time
from typing import Dict, Any
from db.memory_store import AgentMemoryStore
from config.settings import connection_args, openai_config
from langchain_openai import OpenAIEmbeddings
from utils.feishu_alarm import send_error_alarm, send_warning_alarm

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("HealthCheck")

def check_database_connection() -> Dict[str, Any]:
    """
    检查数据库连接状态

    Returns:
        Dict[str, Any]: 检查结果
    """
    result = {
        "status": "unknown",
        "message": "",
        "timestamp": time.time(),
        "details": {}
    }

    try:
        # 初始化embedding模型
        logger.info("初始化OpenAI Embeddings模型")
        embeddings = OpenAIEmbeddings(
            model=openai_config["model"],
            openai_api_key=openai_config["openai_api_key"],
            openai_api_base=openai_config["openai_api_base"]
        )

        # 初始化记忆存储
        logger.info("初始化记忆存储")
        memory_store = AgentMemoryStore(connection_args, embeddings)

        # 执行简单的查询测试
        logger.info("执行数据库连接测试")
        test_results = memory_store.query(
            query="test connection",
            k=1,
            filters=None
        )

        result["status"] = "healthy"
        result["message"] = "数据库连接正常"
        result["details"] = {
            "connection_host": connection_args["host"],
            "connection_port": connection_args["port"],
            "database_name": connection_args["db_name"],
            "test_query_results": len(test_results)
        }

        logger.info("数据库连接检查通过")

    except Exception as e:
        result["status"] = "unhealthy"
        result["message"] = f"数据库连接失败: {str(e)}"
        result["details"] = {
            "error": str(e),
            "connection_host": connection_args.get("host", "unknown"),
            "connection_port": connection_args.get("port", "unknown"),
            "database_name": connection_args.get("db_name", "unknown")
        }

        logger.error(f"数据库连接检查失败: {str(e)}")

    return result

def monitor_database_connection(interval: int = 60, max_failures: int = 3):
    """
    持续监控数据库连接状态

    Args:
        interval: 检查间隔（秒）
        max_failures: 最大连续失败次数
    """
    consecutive_failures = 0

    logger.info(f"开始监控数据库连接，检查间隔: {interval}秒，最大连续失败次数: {max_failures}")

    while True:
        try:
            result = check_database_connection()

            if result["status"] == "healthy":
                consecutive_failures = 0
                logger.info("数据库连接正常")
            else:
                consecutive_failures += 1
                logger.warning(f"数据库连接异常，连续失败次数: {consecutive_failures}/{max_failures}")

                if consecutive_failures >= max_failures:
                    error_msg = f"数据库连接连续失败{consecutive_failures}次，可能需要人工干预"
                    logger.error(error_msg)
                    # 发送严重告警
                    send_error_alarm(error_msg, "数据库连接故障", result.get("message", ""))

            time.sleep(interval)

        except KeyboardInterrupt:
            logger.info("监控被用户中断")
            break
        except Exception as e:
            logger.error(f"监控过程中发生异常: {str(e)}")
            time.sleep(interval)

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "monitor":
        # 持续监控模式
        interval = int(sys.argv[2]) if len(sys.argv) > 2 else 60
        max_failures = int(sys.argv[3]) if len(sys.argv) > 3 else 3
        monitor_database_connection(interval, max_failures)
    else:
        # 单次检查模式
        result = check_database_connection()
        print(f"检查结果: {result}")

        # 根据结果设置退出码
        if result["status"] == "healthy":
            sys.exit(0)
        else:
            sys.exit(1)
