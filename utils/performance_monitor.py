"""
性能监控工具
"""
import time
import logging
from typing import Dict, List, Optional
from functools import wraps
from collections import defaultdict
import json
import threading

logger = logging.getLogger("PerformanceMonitor")

class PerformanceMonitor:
    """性能监控类"""
    
    def __init__(self):
        """初始化性能监控"""
        self._stats = defaultdict(list)
        self._lock = threading.Lock()
        self._start_times = {}
    
    def start_timer(self, operation_name: str, operation_id: Optional[str] = None) -> str:
        """
        开始计时
        
        Args:
            operation_name: 操作名称
            operation_id: 操作ID（可选）
            
        Returns:
            计时器ID
        """
        timer_id = f"{operation_name}_{operation_id or int(time.time() * 1000000)}"
        with self._lock:
            self._start_times[timer_id] = {
                "start_time": time.time(),
                "operation_name": operation_name,
                "operation_id": operation_id
            }
        return timer_id
    
    def end_timer(self, timer_id: str, additional_info: Optional[Dict] = None) -> float:
        """
        结束计时
        
        Args:
            timer_id: 计时器ID
            additional_info: 额外信息
            
        Returns:
            耗时（秒）
        """
        end_time = time.time()
        
        with self._lock:
            if timer_id not in self._start_times:
                logger.warning(f"计时器ID不存在: {timer_id}")
                return 0.0
            
            start_info = self._start_times.pop(timer_id)
            elapsed_time = end_time - start_info["start_time"]
            
            # 记录统计信息
            stat_record = {
                "operation_name": start_info["operation_name"],
                "operation_id": start_info["operation_id"],
                "start_time": start_info["start_time"],
                "end_time": end_time,
                "elapsed_time": elapsed_time,
                "additional_info": additional_info or {}
            }
            
            self._stats[start_info["operation_name"]].append(stat_record)
            
            return elapsed_time
    
    def get_stats(self, operation_name: Optional[str] = None) -> Dict:
        """
        获取统计信息
        
        Args:
            operation_name: 操作名称（可选）
            
        Returns:
            统计信息
        """
        with self._lock:
            if operation_name:
                if operation_name not in self._stats:
                    return {}
                
                records = self._stats[operation_name]
                if not records:
                    return {}
                
                elapsed_times = [r["elapsed_time"] for r in records]
                return {
                    "operation_name": operation_name,
                    "count": len(records),
                    "total_time": sum(elapsed_times),
                    "avg_time": sum(elapsed_times) / len(elapsed_times),
                    "min_time": min(elapsed_times),
                    "max_time": max(elapsed_times),
                    "recent_records": records[-10:]  # 最近10条记录
                }
            else:
                # 返回所有操作的统计信息
                all_stats = {}
                for op_name in self._stats:
                    all_stats[op_name] = self.get_stats(op_name)
                return all_stats
    
    def clear_stats(self, operation_name: Optional[str] = None):
        """
        清除统计信息
        
        Args:
            operation_name: 操作名称（可选，如果不提供则清除所有）
        """
        with self._lock:
            if operation_name:
                if operation_name in self._stats:
                    self._stats[operation_name].clear()
            else:
                self._stats.clear()
    
    def log_stats_summary(self):
        """记录统计信息摘要"""
        stats = self.get_stats()
        if not stats:
            logger.info("暂无性能统计数据")
            return
        
        logger.info("=== 性能统计摘要 ===")
        for op_name, op_stats in stats.items():
            if op_stats:
                logger.info(f"{op_name}: "
                           f"调用次数={op_stats['count']}, "
                           f"平均耗时={op_stats['avg_time']:.3f}s, "
                           f"最小耗时={op_stats['min_time']:.3f}s, "
                           f"最大耗时={op_stats['max_time']:.3f}s")

# 全局性能监控实例
performance_monitor = PerformanceMonitor()

def monitor_performance(operation_name: str):
    """
    性能监控装饰器
    
    Args:
        operation_name: 操作名称
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            timer_id = performance_monitor.start_timer(operation_name)
            try:
                result = func(*args, **kwargs)
                elapsed_time = performance_monitor.end_timer(timer_id)
                logger.info(f"{operation_name} 完成，耗时: {elapsed_time:.3f}秒")
                return result
            except Exception as e:
                performance_monitor.end_timer(timer_id, {"error": str(e)})
                raise
        return wrapper
    return decorator

def log_timing(operation_name: str, elapsed_time: float, additional_info: Optional[Dict] = None):
    """
    记录耗时信息的便捷函数
    
    Args:
        operation_name: 操作名称
        elapsed_time: 耗时
        additional_info: 额外信息
    """
    timer_id = performance_monitor.start_timer(operation_name)
    # 手动设置开始时间
    with performance_monitor._lock:
        if timer_id in performance_monitor._start_times:
            performance_monitor._start_times[timer_id]["start_time"] = time.time() - elapsed_time
    
    performance_monitor.end_timer(timer_id, additional_info)

class TimingContext:
    """计时上下文管理器"""
    
    def __init__(self, operation_name: str, log_result: bool = True):
        """
        初始化计时上下文
        
        Args:
            operation_name: 操作名称
            log_result: 是否记录结果
        """
        self.operation_name = operation_name
        self.log_result = log_result
        self.timer_id = None
        self.elapsed_time = 0
    
    def __enter__(self):
        self.timer_id = performance_monitor.start_timer(self.operation_name)
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.timer_id:
            additional_info = {}
            if exc_type:
                additional_info["error"] = str(exc_val)
            
            self.elapsed_time = performance_monitor.end_timer(self.timer_id, additional_info)
            
            if self.log_result:
                if exc_type:
                    logger.error(f"{self.operation_name} 失败，耗时: {self.elapsed_time:.3f}秒，错误: {exc_val}")
                else:
                    logger.info(f"{self.operation_name} 完成，耗时: {self.elapsed_time:.3f}秒")

# 便捷函数
def get_performance_stats(operation_name: Optional[str] = None) -> Dict:
    """获取性能统计信息"""
    return performance_monitor.get_stats(operation_name)

def clear_performance_stats(operation_name: Optional[str] = None):
    """清除性能统计信息"""
    performance_monitor.clear_stats(operation_name)

def log_performance_summary():
    """记录性能统计摘要"""
    performance_monitor.log_stats_summary()

# 示例用法
if __name__ == "__main__":
    # 使用装饰器
    @monitor_performance("test_operation")
    def test_function():
        time.sleep(0.1)
        return "success"
    
    # 使用上下文管理器
    with TimingContext("test_context"):
        time.sleep(0.05)
    
    # 手动记录
    start = time.time()
    time.sleep(0.02)
    log_timing("manual_timing", time.time() - start, {"data_size": 100})
    
    # 查看统计信息
    log_performance_summary()
    
    # 测试函数
    test_function()
    test_function()
    
    # 再次查看统计信息
    log_performance_summary()
