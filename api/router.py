from fastapi import APIRouter, HTTPException, Request
from typing import Dict, Any, List, Optional
import logging
import time
import json
from models.schemas import AgentMemory, QueryRequest, DeleteRequest, UpdateMemoryByIdRequest, BatchUpdateMemoriesByIdsRequest, BatchDeleteMemoriesByIdsRequest, BatchMemoryOperationsRequest, MemoryOperation
from db.memory_store import AgentMemoryStore
from utils.feishu_alarm import send_error_alarm, send_warning_alarm

# 配置日志
logger = logging.getLogger("api")

# 创建路由器
router = APIRouter()

# 设置全局变量，将在main.py中初始化
memory_store: Optional[AgentMemoryStore] = None

def init_router(store: AgentMemoryStore) -> None:
    """初始化路由器，设置memory_store"""
    global memory_store
    memory_store = store
    logger.info(f"路由器初始化成功，记忆存储对象: {memory_store is not None}")



@router.post("/add_memories/", response_model=Dict[str, Any])
async def create_memories(request: Request, memories: List[AgentMemory]):
    """
    添加Agent记忆到向量存储
    """
    start_time = time.time()
    client_ip = request.client.host if request.client else "unknown"
    logger.info(f"[添加记忆] 收到请求，客户端IP: {client_ip}, 记忆数量: {len(memories)}")

    try:
        # 数据准备阶段
        data_prep_start = time.time()
        memories_list = [memory.model_dump() for memory in memories]
        data_prep_time = time.time() - data_prep_start

        logger.info(f"[添加记忆] 数据准备完成，数量: {len(memories_list)}, 耗时: {data_prep_time:.3f}秒")

        # 添加记忆阶段
        add_memories_start = time.time()
        memory_ids = memory_store.add_memories(memories_list)
        add_memories_time = time.time() - add_memories_start

        total_elapsed_time = time.time() - start_time

        logger.info(f"[添加记忆] 添加成功，数量: {len(memory_ids)}, "
                   f"数据准备耗时: {data_prep_time:.3f}秒, "
                   f"添加记忆耗时: {add_memories_time:.3f}秒, "
                   f"总耗时: {total_elapsed_time:.3f}秒")

        return {
            "status": "success",
            "message": f"已写入{len(memories)}条agent记忆",
            "ids": memory_ids,
            "timing": {
                "data_preparation": f"{data_prep_time:.3f}s",
                "add_memories": f"{add_memories_time:.3f}s",
                "total": f"{total_elapsed_time:.3f}s"
            }
        }
    except Exception as e:
        elapsed_time = time.time() - start_time
        error_msg = f"[添加记忆] 失败，错误: {str(e)}, 耗时: {elapsed_time:.3f}秒"
        logger.error(error_msg, exc_info=True)

        # 发送错误告警
        send_error_alarm(f"API接口异常: {error_msg}", "API异常", str(e))

        raise HTTPException(status_code=500, detail=str(e))

@router.post("/query/", response_model=Dict[str, Any])
async def query(req: Request, request: QueryRequest):
    """
    根据查询文本，检索相似的记忆内容，使用双向量搜索和重排序

    逻辑：根据query匹配topic和content的向量进行查询，再计算得分排序返回

    参数：
    - query: 查询文本（必需）
    - k: 返回结果数量
    - filters: 过滤条件
    - importance_weight: 重要程度权重 (0-1)
    - confidence_weight: 可信度权重 (0-1)
    - recency_weight: 时效性权重 (0-1)
    - content_weights: 内容向量权重 (0-1)
    - topic_weights: 话题向量权重 (0-1)

    返回值新增：
    - content_distance: 内容向量距离（原始距离，未加权）
    - topic_distance: 话题向量距离（原始距离，未加权）
    """
    start_time = time.time()
    client_ip = req.client.host if req.client else "unknown"
    logger.info(f"[查询相似] 收到请求，客户端IP: {client_ip}, 查询文本: {request.query}, k: {request.k}")

    try:
        # 验证参数
        if not request.query:
            logger.warning(f"[查询相似] 查询文本为空, 客户端IP: {client_ip}")
            raise HTTPException(status_code=400, detail="query is required")

        if request.k <= 0:
            logger.warning(f"[查询相似] 参数k无效: {request.k}, 客户端IP: {client_ip}")
            raise HTTPException(status_code=400, detail="k must be positive")

        # 验证权重参数
        if request.importanceWeight < 0 or request.importanceWeight > 1:
            logger.warning(f"[查询相似] 参数importanceWeight无效: {request.importanceWeight}, 客户端IP: {client_ip}")
            raise HTTPException(status_code=400, detail="importanceWeight must be between 0 and 1")

        if request.confidenceWeight < 0 or request.confidenceWeight > 1:
            logger.warning(f"[查询相似] 参数confidenceWeight无效: {request.confidenceWeight}, 客户端IP: {client_ip}")
            raise HTTPException(status_code=400, detail="confidenceWeight must be between 0 and 1")

        if request.recencyWeight < 0 or request.recencyWeight > 1:
            logger.warning(f"[查询相似] 参数recencyWeight无效: {request.recencyWeight}, 客户端IP: {client_ip}")
            raise HTTPException(status_code=400, detail="recencyWeight must be between 0 and 1")

        if request.contentWeights < 0 or request.contentWeights > 1:
            logger.warning(f"[查询相似] 参数contentWeights无效: {request.contentWeights}, 客户端IP: {client_ip}")
            raise HTTPException(status_code=400, detail="contentWeights must be between 0 and 1")

        if request.topicWeights < 0 or request.topicWeights > 1:
            logger.warning(f"[查询相似] 参数topicWeights无效: {request.topicWeights}, 客户端IP: {client_ip}")
            raise HTTPException(status_code=400, detail="topicWeights must be between 0 and 1")

        # 参数验证阶段
        param_validation_time = time.time() - start_time

        logger.info(f"[查询相似] 参数验证完成，耗时: {param_validation_time:.3f}秒")
        logger.info(f"[查询相似] 开始查询，查询文本: {request.query}, 过滤条件: {json.dumps(request.filters, ensure_ascii=False) if request.filters else 'None'}, k: {request.k}")

        # 查询阶段 - 使用新的双向量搜索和重排序方法
        query_start = time.time()
        results = memory_store.query(
            query=request.query,
            k=request.k,
            filters=request.filters,
            importance_weight=request.importanceWeight,
            confidence_weight=request.confidenceWeight,
            recency_weight=request.recencyWeight,
            content_weights=request.contentWeights,
            topic_weights=request.topicWeights
        )
        query_time = time.time() - query_start

        total_elapsed_time = time.time() - start_time

        logger.info(f"[查询相似] 查询成功，结果数量: {len(results)}, "
                   f"参数验证耗时: {param_validation_time:.3f}秒, "
                   f"查询耗时: {query_time:.3f}秒, "
                   f"总耗时: {total_elapsed_time:.3f}秒")

        # 记录返回的距离信息
        if results:
            sample_result = results[0]
            logger.info(f"[查询相似] 返回结果包含距离信息 - 综合距离: {sample_result.get('distance', 'N/A')}, "
                       f"内容距离: {sample_result.get('content_distance', 'N/A')}, "
                       f"话题距离: {sample_result.get('topic_distance', 'N/A')}")

        return {
            "status": "success",
            "results": results,
            "timing": {
                "parameter_validation": f"{param_validation_time:.3f}s",
                "query_execution": f"{query_time:.3f}s",
                "total": f"{total_elapsed_time:.3f}s"
            }
        }
    except ValueError as e:
        elapsed_time = time.time() - start_time
        logger.error(f"[查询相似] 参数错误，错误: {str(e)}, 耗时: {elapsed_time:.3f}秒")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        elapsed_time = time.time() - start_time
        error_msg = f"[查询相似] 失败，错误: {str(e)}, 耗时: {elapsed_time:.3f}秒"
        logger.error(error_msg, exc_info=True)

        # 发送错误告警
        send_error_alarm(f"API接口异常: {error_msg}", "API异常", str(e))

        raise HTTPException(status_code=500, detail=str(e))

@router.post("/delete/", response_model=Dict[str, str])
async def delete_memories(request: DeleteRequest):
    """
    根据过滤条件删除符合的记忆数据
    """

    try:
        logger.info(f"[删除记忆] 收到请求，过滤条件: {json.dumps(request.filterDict, ensure_ascii=False)}")
        deleted_count = memory_store.delete_memories_by_filter(request.filterDict)
        return {"status": "success", "message": f"已删除{deleted_count}条符合条件的数据"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))



@router.post("/memories/update/{memory_id}", response_model=Dict[str, str])
async def update_memory_by_id(memory_id: str, request: UpdateMemoryByIdRequest):
    """
    根据ID更新记忆数据
    """
    try:
        logger.info(f"[更新记忆] 收到请求，ID: {memory_id}, 更新数据: {json.dumps(request.newData, ensure_ascii=False)}")
        success = memory_store.update_memory(memory_id, request.newData)
        if not success:
            return {"status": "warning", "message": f"未找到ID为{memory_id}的记录"}
        return {"status": "success", "message": f"成功更新ID为{memory_id}的记录"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/memories/batch_update", response_model=Dict[str, str])
async def batch_update_memories(request: BatchUpdateMemoriesByIdsRequest):
    """
    批量根据ID更新记忆数据
    """
    try:
        logger.info(f"[批量更新记忆] 收到请求，更新项数量: {len(request.items)}")
        updated_count = memory_store.batch_update_memories(request.items)
        return {
            "status": "success",
            "message": f"成功更新{updated_count}条记录"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/memories/delete/{memory_id}", response_model=Dict[str, str])
async def delete_memory_by_id(memory_id: str):
    """
    根据ID删除记忆数据
    """
    try:
        logger.info(f"[删除记忆] 收到请求，ID: {memory_id}")
        success = memory_store.delete_memory(memory_id)
        if not success:
            return {"status": "warning", "message": f"未找到ID为{memory_id}的记录"}
        return {"status": "success", "message": f"成功删除ID为{memory_id}的记录"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/memories/batch_delete", response_model=Dict[str, str])
async def batch_delete_memories(request: BatchDeleteMemoriesByIdsRequest):
    """
    批量根据ID删除记忆数据
    """
    try:
        logger.info(f"[批量删除记忆] 收到请求，ID数量: {len(request.ids)}")
        deleted_count = memory_store.batch_delete_memories(request.ids)
        return {
            "status": "success",
            "message": f"成功删除{deleted_count}条记录"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/memories/operations", response_model=Dict[str, Any])
async def memory_operations(req: Request, request: BatchMemoryOperationsRequest):
    """
    综合接口：批量处理记忆的新建、更新和删除操作

    支持以下操作：
    - create: 批量创建新记忆
    - update: 批量更新现有记忆
    - delete: 批量删除记忆

    可以在一个请求中批量执行多种不同操作
    """
    start_time = time.time()
    client_ip = req.client.host if req.client else "unknown"
    logger.info(f"[综合操作] 收到请求，客户端IP: {client_ip}, 操作数量: {len(request.operations)}")

    results = {
        "status": "success",
        "operations": {
            "create": {"count": 0, "ids": []},
            "update": {"count": 0, "ids": []},
            "delete": {"count": 0, "ids": []}
        },
        "errors": []
    }

    try:
        for i, op in enumerate(request.operations):
            try:
                # 创建操作
                if op.operation == "create":
                    logger.info(f"[综合操作] 开始执行创建操作，索引: {i}")
                    await operations_create(i, op, results)
                # 更新操作
                elif op.operation == "update":
                    logger.info(f"[综合操作] 开始执行更新操作，索引: {i}")
                    await operations_update(i, op, results)
                # 删除操作
                elif op.operation == "delete":
                    logger.info(f"[综合操作] 开始执行删除操作，索引: {i}")
                    await operations_delete(i, op, results)
            except Exception as e:
                logger.error(f"[综合操作] 操作失败，索引: {i}, 操作类型: {op.operation}, 错误: {str(e)}")
                results["errors"].append({
                    "index": i,
                    "operation": op.operation,
                    "error": str(e)
                })

        # 如果所有操作都失败，返回错误状态
        if (results["operations"]["create"]["count"] == 0 and
            results["operations"]["update"]["count"] == 0 and
            results["operations"]["delete"]["count"] == 0 and
            len(results["errors"]) > 0):
            results["status"] = "error"
            logger.error(f"[综合操作] 所有操作失败，错误数量: {len(results['errors'])}")
        # 如果有部分操作失败，返回部分成功状态
        elif len(results["errors"]) > 0:
            results["status"] = "partial_success"
            logger.warning(f"[综合操作] 部分操作失败，成功操作数: {results['operations']['create']['count'] + results['operations']['update']['count'] + results['operations']['delete']['count']}, 失败操作数: {len(results['errors'])}")

        elapsed_time = time.time() - start_time
        logger.info(f"[综合操作] 完成，状态: {results['status']}, 创建: {results['operations']['create']['count']}, 更新: {results['operations']['update']['count']}, 删除: {results['operations']['delete']['count']}, 错误: {len(results['errors'])}, 耗时: {elapsed_time:.3f}秒")
        return results
    except Exception as e:
        elapsed_time = time.time() - start_time
        logger.error(f"[综合操作] 失败，错误: {str(e)}, 耗时: {elapsed_time:.3f}秒", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


async def operations_create(i, op, results):
    # 批量创建
    if op.memories:
        # 处理批量创建请求
        memories_list = []
        for memory in op.memories:
            # 如果是AgentMemory对象，转换为字典
            if isinstance(memory, AgentMemory):
                memories_list.append(memory.model_dump())
            else:
                memories_list.append(memory)

        # 批量添加记忆
        memory_ids = memory_store.add_memories(memories_list)
        results["operations"]["create"]["count"] += len(memory_ids)
        results["operations"]["create"]["ids"].extend(memory_ids)

    # 单条创建（兼容旧版本）
    elif op.memory_data:
        # 如果提供的是AgentMemory对象，转换为字典
        memory_dict = op.memory_data
        if isinstance(memory_dict, AgentMemory):
            memory_dict = memory_dict.model_dump()

        # 添加记忆
        memory_id = memory_store.add_memories([memory_dict])[0]
        results["operations"]["create"]["count"] += 1
        results["operations"]["create"]["ids"].append(memory_id)
    else:
        results["errors"].append({
            "index": i,
            "operation": "create",
            "error": "创建操作必须提供memories或memory_data"
        })


async def operations_update(i, op, results):
    # 批量更新
    if op.updates:
        logger.info(f"[更新操作] 开始批量更新，数量: {len(op.updates)}")
        # 使用批量更新接口
        updated_count = memory_store.batch_update_memories(op.updates)

        # 收集所有更新的ID
        updated_ids = [item.get("id") for item in op.updates if "id" in item]
        logger.info(f"[更新操作] 批量更新完成，成功数量: {updated_count}, IDs: {updated_ids}")

        results["operations"]["update"]["count"] += updated_count
        results["operations"]["update"]["ids"].extend(updated_ids)

        # 如果更新数量与请求数量不符，添加警告
        if updated_count < len(op.updates):
            error_msg = f"部分更新失败，请求数量{len(op.updates)}，成功数量{updated_count}"
            logger.warning(f"[更新操作] {error_msg}")
            results["errors"].append({
                "index": i,
                "operation": "update",
                "error": error_msg
            })

    # 单条更新（兼容旧版本）
    elif op.memory_id and op.memory_data:
        logger.info(f"[更新操作] 开始单条更新，ID: {op.memory_id}")
        # 如果提供的是AgentMemory对象，转换为字典
        new_data = op.memory_data
        if isinstance(new_data, AgentMemory):
            new_data = new_data.model_dump()

        # 更新记忆
        success = memory_store.update_memory(op.memory_id, new_data)
        if success:
            logger.info(f"[更新操作] 单条更新成功，ID: {op.memory_id}")
            results["operations"]["update"]["count"] += 1
            results["operations"]["update"]["ids"].append(op.memory_id)
        else:
            error_msg = f"未找到ID为{op.memory_id}的记录"
            logger.warning(f"[更新操作] {error_msg}")
            results["errors"].append({
                "index": i,
                "operation": "update",
                "error": error_msg
            })
    else:
        error_msg = "更新操作必须提供updates或(memory_id和memory_data)"
        logger.error(f"[更新操作] {error_msg}")
        results["errors"].append({
            "index": i,
            "operation": "update",
            "error": error_msg
        })


async def operations_delete(i, op, results):
    # 批量删除（根据ID列表）
    if op.memory_ids:
        deleted_count = memory_store.batch_delete_memories(op.memory_ids)
        results["operations"]["delete"]["count"] += deleted_count
        results["operations"]["delete"]["ids"].extend(op.memory_ids[:deleted_count])  # 只添加成功删除的ID

        # 如果删除数量与请求数量不符，添加警告
        if deleted_count < len(op.memory_ids):
            results["errors"].append({
                "index": i,
                "operation": "delete",
                "error": f"部分删除失败，请求数量{len(op.memory_ids)}，成功数量{deleted_count}"
            })

    # 单条删除（兼容旧版本）
    elif op.memory_id:
        success = memory_store.delete_memory(op.memory_id)
        if success:
            results["operations"]["delete"]["count"] += 1
            results["operations"]["delete"]["ids"].append(op.memory_id)
        else:
            results["errors"].append({
                "index": i,
                "operation": "delete",
                "error": f"未找到ID为{op.memory_id}的记录"
            })

    # 根据过滤条件删除
    elif op.filter_dict:
        deleted_count = memory_store.delete_memories_by_filter(op.filter_dict)
        results["operations"]["delete"]["count"] += deleted_count
    else:
        results["errors"].append({
            "index": i,
            "operation": "delete",
            "error": "删除操作必须提供memory_ids、memory_id或filter_dict"
        })
