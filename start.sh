#!/bin/bash

# 创建必要的目录
mkdir -p /data/log/memory-server

# 设置权限
chmod -R 777 /data/log

# 输出环境信息
echo "Python version:"
python --version

echo "Pip version:"
pip --version

echo "Installed packages:"
pip list

# 检查gunicorn是否安装
echo "Gunicorn version:"
gunicorn --version

# 确保配置文件存在
if [ ! -f /app/config/gunicorn_conf.py ]; then
    echo "Error: gunicorn_conf.py not found!"
    exit 1
fi

if [ ! -f /app/config/gunicorn_health_conf.py ]; then
    echo "Error: gunicorn_health_conf.py not found!"
    exit 1
fi

# 启动supervisord
echo "Starting supervisord..."
supervisord_path=$(which supervisord)
exec $supervisord_path -c /app/supervisord.conf
