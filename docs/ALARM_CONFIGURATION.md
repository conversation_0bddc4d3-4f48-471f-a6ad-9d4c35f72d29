# 飞书告警配置指南

## 概述

本服务集成了飞书告警功能，可以在发生异常时自动发送告警消息到飞书群组。

## 配置步骤

### 1. 创建飞书机器人

1. 在飞书群组中，点击右上角的设置按钮
2. 选择"群机器人" -> "添加机器人" -> "自定义机器人"
3. 设置机器人名称和描述
4. 选择"加签"安全设置
5. 复制生成的 Webhook 地址和密钥

### 2. 配置环境变量

#### 方式一：通过环境变量配置

```bash
export ALARM_FS_WEBHOOK="https://open.feishu.cn/open-apis/bot/v2/hook/xxxxxxxx"
export ALARM_FS_SECRET="your_secret_key"
export SERVICE_NAME="memory-server"
```

#### 方式二：通过 Docker 环境变量配置

```bash
docker run -d \
  -e ALARM_FS_WEBHOOK="https://open.feishu.cn/open-apis/bot/v2/hook/xxxxxxxx" \
  -e ALARM_FS_SECRET="your_secret_key" \
  -e SERVICE_NAME="memory-server" \
  -p 8000:8000 \
  -p 9195:9195 \
  memory-server:latest
```

#### 方式三：通过 docker-compose 配置

```yaml
version: '3.8'
services:
  memory-server:
    image: memory-server:latest
    ports:
      - "8000:8000"
      - "9195:9195"
    environment:
      - ALARM_FS_WEBHOOK=https://open.feishu.cn/open-apis/bot/v2/hook/xxxxxxxx
      - ALARM_FS_SECRET=your_secret_key
      - SERVICE_NAME=memory-server
```

## 告警类型

### 1. 错误告警 (🚨)

- 数据库连接失败
- 服务初始化失败
- API接口异常
- 数据库操作失败

### 2. 警告告警 (⚠️)

- 数据库连接异常但可恢复
- 部分操作失败
- 性能警告

### 3. 信息告警 (ℹ️)

- 服务启动成功
- 重要操作完成

## 告警消息格式

```
🚨 【记忆向量存储服务告警 服务(memory-server)】

错误类型: 数据库连接失败
错误信息: Lost connection to MySQL server during query
堆栈跟踪: ...

时间: 2024-01-01 12:00:00
```

## 防重复告警机制

- 相同类型的告警在5分钟内只会发送一次
- 通过消息内容的哈希值进行去重
- 避免告警风暴

## 测试告警功能

### 手动测试

```python
from utils.feishu_alarm import send_error_alarm, send_warning_alarm, send_info_alarm

# 发送测试告警
send_info_alarm("这是一条测试信息")
send_warning_alarm("这是一条测试警告")
send_error_alarm("这是一条测试错误", "测试错误类型", "测试堆栈信息")
```

### 通过API测试

```bash
# 触发一个不存在的API来测试错误告警
curl -X GET http://localhost:8000/test-error
```

## 故障排查

### 1. 告警未发送

检查以下配置：

```bash
# 检查环境变量是否设置
echo $ALARM_FS_WEBHOOK
echo $ALARM_FS_SECRET

# 检查日志
grep -i "飞书告警" /data/log/memory-server/*.log
```

### 2. 告警发送失败

常见问题：

- Webhook 地址错误
- 密钥错误
- 网络连接问题
- 飞书机器人被禁用

### 3. 告警消息格式异常

检查日志中的错误信息：

```bash
grep -i "发送飞书告警失败" /data/log/memory-server/*.log
```

## 最佳实践

### 1. 告警分级

- **严重告警**：服务不可用、数据库连接完全失败
- **警告告警**：部分功能异常、性能下降
- **信息告警**：服务状态变更、重要操作完成

### 2. 告警频率控制

- 避免在短时间内发送大量重复告警
- 对于持续性问题，建议设置告警升级机制

### 3. 告警内容优化

- 包含足够的上下文信息
- 提供可操作的建议
- 避免过于技术性的术语

## 扩展功能

### 1. 添加其他告警渠道

可以扩展 `utils/feishu_alarm.py` 来支持其他告警渠道：

- 邮件告警
- 短信告警
- 钉钉告警
- 企业微信告警

### 2. 告警规则配置

可以添加配置文件来定义不同类型错误的告警规则：

```json
{
  "alarm_rules": {
    "database_connection": {
      "level": "error",
      "interval": 300,
      "escalation": true
    },
    "api_error": {
      "level": "warning",
      "interval": 600,
      "escalation": false
    }
  }
}
```

### 3. 告警统计和分析

可以添加告警统计功能，分析告警趋势和模式。
