# 数据库连接问题排查指南

## 问题描述

在运行一段时间后，可能会遇到以下错误：

```
Lost connection to MySQL server during query
```

这是一个常见的数据库连接超时问题，通常由以下原因引起：

1. 数据库连接超时
2. 网络不稳定
3. 数据库服务器重启
4. 连接池配置不当

## 解决方案

### 1. 自动重连机制

我们已经在代码中实现了自动重连机制：

- 所有数据库操作方法都添加了 `@retry_on_connection_error` 装饰器
- 当检测到连接错误时，会自动重试最多3次
- 每次重试之间有指数退避延迟
- 重试时会重新建立数据库连接

### 2. 连接池配置

在 `config/settings.py` 中配置了连接池参数：

```python
"connection_args": {
    "pool_size": 10,           # 连接池大小
    "max_overflow": 20,        # 最大溢出连接数
    "pool_timeout": 30,        # 连接超时时间
    "pool_recycle": 3600,      # 1小时后回收连接
    "pool_pre_ping": True      # 连接前检查连接是否有效
}
```

### 3. 健康检查

#### 手动健康检查

```bash
python -m utils.health_check
```

#### 持续监控

```bash
# 每60秒检查一次，连续失败3次后告警
python -m utils.health_check monitor 60 3
```

### 4. 启动时健康检查

服务启动时会自动进行数据库连接检查：

```bash
./start.sh
```

如果数据库连接失败，会等待30秒后重试一次。

## 监控和告警

### 日志监控

查看应用日志中的连接相关信息：

```bash
# 查看连接错误
grep -i "connection" /data/log/memory-server/*.log

# 查看重连日志
grep -i "reconnect" /data/log/memory-server/*.log

# 查看健康检查日志
grep -i "health" /data/log/memory-server/*.log
```

### 关键日志信息

- `数据库连接错误，第X次重试`: 表示检测到连接错误并开始重试
- `数据库重连成功`: 表示重连成功
- `数据库重连失败`: 表示重连失败，可能需要人工干预
- `数据库连接检查通过`: 表示健康检查通过

## 预防措施

### 1. 定期健康检查

建议设置定时任务，定期检查数据库连接状态：

```bash
# 添加到 crontab
*/5 * * * * /usr/bin/python -m utils.health_check >> /var/log/db_health.log 2>&1
```

### 2. 监控告警

可以集成到监控系统中，当连续失败次数超过阈值时发送告警。

### 3. 数据库配置优化

在数据库端优化以下参数：

```sql
-- 增加连接超时时间
SET GLOBAL wait_timeout = 28800;
SET GLOBAL interactive_timeout = 28800;

-- 增加最大连接数
SET GLOBAL max_connections = 1000;
```

## 故障排查步骤

1. **检查网络连接**
   ```bash
   ping 10.64.230.67
   telnet 10.64.230.67 2883
   ```

2. **检查数据库服务状态**
   ```bash
   # 连接数据库
   mysql -h 10.64.230.67 -P 2883 -u ebc_agent_memory_rw@ob_test_ebc#test_ttcluster_001 -p
   ```

3. **检查应用日志**
   ```bash
   tail -f /data/log/memory-server/*.log
   ```

4. **手动健康检查**
   ```bash
   python -m utils.health_check
   ```

5. **重启服务**
   ```bash
   # 如果问题持续，重启服务
   supervisorctl restart all
   ```

## 常见问题

### Q: 为什么会出现连接丢失？

A: 主要原因包括：
- 数据库连接空闲时间过长被服务器关闭
- 网络不稳定导致连接中断
- 数据库服务器负载过高
- 防火墙或代理服务器超时

### Q: 重连机制是否会影响性能？

A: 重连机制只在检测到连接错误时才会触发，正常情况下不会影响性能。重连过程中可能会有短暂的延迟，但这比服务完全不可用要好。

### Q: 如何调整重试参数？

A: 可以修改 `@retry_on_connection_error` 装饰器的参数：
```python
@retry_on_connection_error(max_retries=5, delay=2)  # 最多重试5次，延迟2秒
```

### Q: 如何禁用自动重连？

A: 如果需要禁用自动重连，可以移除相关方法上的 `@retry_on_connection_error` 装饰器。但不建议这样做，因为这会降低服务的可用性。
