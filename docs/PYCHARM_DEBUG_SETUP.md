# PyCharm 调试配置指南

## 问题描述

在 PyCharm 中调试时可能遇到配置加载失败的问题，主要原因是环境变量未正确设置。

## 解决方案

### 方法1：设置环境变量

1. 在 PyCharm 中打开 `main.py` 文件
2. 点击右上角的运行配置下拉菜单
3. 选择 "Edit Configurations..."
4. 在 "Environment variables" 部分添加以下环境变量：
   ```
   APP_ENV=dev
   ```
5. 点击 "OK" 保存配置

### 方法2：修改代码中的默认环境

如果不想设置环境变量，可以直接修改 `config/settings.py` 中的默认环境：

```python
# 获取当前环境
ENV = os.environ.get("APP_ENV", "dev")  # 确保默认值是 "dev"
```

### 方法3：使用运行时参数

在 PyCharm 的运行配置中，可以在 "Parameters" 字段中添加：
```
--env=dev
```

然后修改 `main.py` 来处理这个参数。

## 验证配置

运行以下代码来验证配置是否正确加载：

```python
import os
os.environ["APP_ENV"] = "dev"

from config.settings import connection_args, openai_config
print("配置加载成功！")
print(f"数据库主机: {connection_args.get('host', 'N/A')}")
print(f"OpenAI模型: {openai_config.get('model', 'N/A')}")
```

## 常见问题

### 1. KeyError: 'host'

**原因**: `connection_args` 为空字典，通常是因为环境配置未正确加载。

**解决方案**: 
- 检查 `APP_ENV` 环境变量是否设置为 `dev`、`test` 或 `prod`
- 确保 `config/settings.py` 中的环境配置映射正确

### 2. 飞书告警配置未找到

**原因**: 告警配置从环境变量读取，但环境变量未设置。

**解决方案**: 
- 告警配置现在从配置文件读取，无需设置环境变量
- 如果需要自定义告警配置，可以在对应环境的配置中修改

### 3. OpenAI API 配置错误

**原因**: OpenAI 配置不完整或格式错误。

**解决方案**: 
- 检查 `openai_config` 中的 `openai_api_key` 和 `openai_api_base` 是否正确
- 确保 API 密钥格式正确

## 推荐的 PyCharm 配置

### 运行配置

1. **Name**: Memory Server Debug
2. **Script path**: `/path/to/your/project/main.py`
3. **Parameters**: (留空)
4. **Environment variables**: 
   ```
   APP_ENV=dev
   PYTHONPATH=/path/to/your/project
   ```
5. **Python interpreter**: 选择项目的 Python 解释器
6. **Working directory**: `/path/to/your/project`

### 调试配置

调试配置与运行配置相同，只需要在调试模式下运行即可。

## 环境配置说明

### 开发环境 (dev)
- 使用测试数据库
- 启用详细日志
- 使用开发环境的 OpenAI 配置

### 测试环境 (test)
- 使用测试数据库
- 启用测试模式
- 可能使用模拟的 OpenAI 服务

### 生产环境 (prod)
- 使用生产数据库
- 优化的日志级别
- 生产环境的 OpenAI 配置

## 故障排查

### 1. 查看当前环境

在代码中添加调试信息：

```python
import os
from config.settings import ENV, config
print(f"当前环境: {ENV}")
print(f"配置键: {list(config.keys())}")
```

### 2. 检查配置合并

在 `get_config()` 函数中添加调试信息：

```python
def get_config() -> Dict[str, Any]:
    config = base_config.copy()
    print(f"基础配置: {list(config.keys())}")
    
    if ENV in env_configs:
        config = merge_configs(config, env_configs[ENV])
        print(f"合并环境配置后: {list(config.keys())}")
    
    return config
```

### 3. 验证数据库连接

```python
from config.settings import connection_args
print(f"数据库配置: {connection_args}")

# 测试连接
from db.memory_store import AgentMemoryStore
try:
    store = AgentMemoryStore(connection_args, None)
    print("数据库连接成功")
except Exception as e:
    print(f"数据库连接失败: {e}")
```

通过以上配置和检查，应该能够解决 PyCharm 调试时的配置问题。
