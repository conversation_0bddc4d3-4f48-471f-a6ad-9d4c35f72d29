# 性能监控和耗时统计指南

## 概述

本服务集成了详细的性能监控和耗时统计功能，可以帮助您了解各个操作的性能表现，识别性能瓶颈，并优化系统性能。

## 耗时统计功能

### 1. API接口耗时统计

所有API接口都会记录详细的耗时信息，包括：

#### 添加记忆接口 (`/add_memories/`)

```json
{
  "status": "success",
  "message": "已写入10条agent记忆",
  "ids": ["712345678901234567", "..."],
  "timing": {
    "data_preparation": "0.002s",
    "add_memories": "1.234s",
    "total": "1.236s"
  }
}
```

**耗时分解：**
- `data_preparation`: 数据准备和验证时间
- `add_memories`: 实际添加记忆的时间（包括向量生成和数据库插入）
- `total`: 总耗时

#### 查询记忆接口 (`/query/`)

```json
{
  "status": "success",
  "results": [...],
  "timing": {
    "parameter_validation": "0.001s",
    "query_execution": "0.456s",
    "total": "0.457s"
  }
}
```

**耗时分解：**
- `parameter_validation`: 参数验证时间
- `query_execution`: 查询执行时间（包括向量转换、数据库搜索、重排序）
- `total`: 总耗时

### 2. 数据库操作耗时统计

#### 向量嵌入生成

```
[INFO] 向量嵌入生成完成，耗时: 0.123秒
[INFO] 批量向量嵌入进度: 100/100, 平均耗时: 0.098秒/条
```

#### 数据库操作

```
[INFO] 成功添加记忆: 712345678901234567, 向量嵌入耗时: 0.123秒, 数据库插入耗时: 0.045秒
[INFO] 查询到5条相似记忆, 数据库搜索耗时: 0.234秒, 结果处理耗时: 0.012秒
```

#### 重排序操作

```
[INFO] 重排序完成，计算得分耗时: 0.008秒, 排序耗时: 0.002秒, 重排序总耗时: 0.010秒, 返回5条结果
```

## 性能监控工具

### 1. 性能监控类 (`utils/performance_monitor.py`)

提供了完整的性能监控功能：

```python
from utils.performance_monitor import TimingContext, monitor_performance

# 使用上下文管理器
with TimingContext("custom_operation"):
    # 执行操作
    pass

# 使用装饰器
@monitor_performance("my_function")
def my_function():
    # 函数实现
    pass
```

### 2. 性能测试工具 (`utils/test_performance.py`)

提供了自动化的性能测试：

```bash
# 运行性能测试
python -m utils.test_performance

# 测试结果会保存到 performance_report_<timestamp>.md
```

## 日志分析

### 1. 查看实时性能日志

```bash
# 查看所有性能相关日志
tail -f /data/log/memory-server/*.log | grep -E "(耗时|timing)"

# 查看向量嵌入耗时
tail -f /data/log/memory-server/*.log | grep "向量嵌入"

# 查看数据库操作耗时
tail -f /data/log/memory-server/*.log | grep -E "(数据库|搜索耗时)"
```

### 2. 性能日志格式

```
2024-01-01 12:00:00 - AgentMemoryStore - INFO - 向量嵌入生成完成，耗时: 0.123秒
2024-01-01 12:00:00 - AgentMemoryStore - INFO - 数据库搜索耗时: 0.234秒, 结果处理耗时: 0.012秒
2024-01-01 12:00:00 - api - INFO - [查询记忆] 查询成功，结果数量: 5, 总耗时: 0.457秒
```

## 性能优化建议

### 1. 向量嵌入优化

**问题识别：**
- 向量嵌入耗时过长（>0.5秒/条）
- 批量操作时平均耗时过高

**优化方案：**
- 使用更快的嵌入模型
- 批量处理向量嵌入
- 考虑使用本地嵌入模型

### 2. 数据库查询优化

**问题识别：**
- 数据库搜索耗时过长（>1秒）
- 结果处理耗时过高

**优化方案：**
- 优化索引配置
- 调整查询参数
- 减少返回字段

### 3. API响应优化

**问题识别：**
- 总耗时过长
- 某个阶段耗时异常

**优化方案：**
- 异步处理
- 缓存机制
- 连接池优化

## 性能基准

### 1. 正常性能指标

| 操作 | 预期耗时 | 说明 |
|------|----------|------|
| 单条记忆添加 | <0.5秒 | 包括向量生成和数据库插入 |
| 批量记忆添加 | <0.1秒/条 | 平均每条记忆的处理时间 |
| 向量查询 | <0.3秒 | 不包括向量生成时间 |
| 重排序 | <0.05秒 | 100条结果内的重排序 |

### 2. 性能告警阈值

| 操作 | 警告阈值 | 错误阈值 |
|------|----------|----------|
| 向量嵌入 | >0.5秒 | >2秒 |
| 数据库查询 | >1秒 | >5秒 |
| API总耗时 | >2秒 | >10秒 |

## 故障排查

### 1. 性能问题诊断

**步骤1：查看日志**
```bash
# 查看最近的性能日志
tail -100 /data/log/memory-server/*.log | grep -E "(耗时|timing)"
```

**步骤2：运行性能测试**
```bash
python -m utils.test_performance
```

**步骤3：分析瓶颈**
- 向量嵌入耗时过长：检查OpenAI API连接
- 数据库查询慢：检查数据库连接和索引
- 总耗时过长：检查网络和系统资源

### 2. 常见性能问题

**问题1：向量嵌入超时**
```
[ERROR] 向量嵌入生成失败: timeout
```
**解决方案：**
- 检查OpenAI API配置
- 增加超时时间
- 使用本地嵌入模型

**问题2：数据库连接超时**
```
[ERROR] 数据库搜索耗时: 10.234秒
```
**解决方案：**
- 检查数据库连接
- 优化查询条件
- 增加连接池大小

**问题3：内存使用过高**
```
[WARNING] 批量处理内存使用过高
```
**解决方案：**
- 减少批处理大小
- 增加系统内存
- 优化数据结构

## 监控集成

### 1. 与告警系统集成

性能监控可以与飞书告警系统集成：

```python
from utils.feishu_alarm import send_warning_alarm

# 性能告警示例
if elapsed_time > 5.0:
    send_warning_alarm(f"操作耗时过长: {operation_name}, 耗时: {elapsed_time:.3f}秒")
```

### 2. 与监控系统集成

可以将性能数据导出到外部监控系统：

```python
# 导出性能数据
stats = get_performance_stats()
# 发送到Prometheus、Grafana等监控系统
```

## 最佳实践

### 1. 性能监控

- 定期查看性能日志
- 设置性能告警阈值
- 建立性能基准线

### 2. 性能优化

- 优先优化高频操作
- 关注用户体验关键路径
- 平衡性能和准确性

### 3. 容量规划

- 根据性能数据规划容量
- 预测性能瓶颈
- 制定扩容策略

## 扩展功能

### 1. 自定义性能监控

```python
from utils.performance_monitor import TimingContext

# 自定义监控点
with TimingContext("custom_operation") as timer:
    # 执行操作
    result = complex_operation()
    
# 获取耗时
print(f"操作耗时: {timer.elapsed_time:.3f}秒")
```

### 2. 性能数据分析

```python
from utils.performance_monitor import get_performance_stats

# 获取统计数据
stats = get_performance_stats("add_memories")
print(f"平均耗时: {stats['avg_time']:.3f}秒")
print(f"最大耗时: {stats['max_time']:.3f}秒")
```

通过这些性能监控和耗时统计功能，您可以全面了解系统的性能表现，及时发现和解决性能问题，确保服务的高效运行。
